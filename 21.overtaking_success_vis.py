"""
追越成功场景可视化系统
基于20.overtaking_success_extraction.py的追越成功场景提取结果，进行可视化展示

功能：
1. 读取追越成功场景数据
2. 提取场景中的船舶轨迹信息
3. 动态可视化追越过程
4. 标记位置变化时刻
5. 保存动画和静态图片

输出：
- vis/overtaking_success_animations/: 追越成功动画
- vis/overtaking_success_images/: 追越成功静态图片
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import random
import pandas as pd
from pathlib import Path
from tqdm import tqdm


def extract_trajectories_from_overtaking_scene(scene_data, tras_df):
    """
    从追越成功场景中提取轨迹数据
    
    :param scene_data: 追越成功场景数据，包含完整场景信息
    :param tras_df: 船舶轨迹数据DataFrame
    :return: 提取的轨迹数据列表，按时间点分组
    """
    # 获取场景中的船舶信息
    ship1_mmsi = scene_data['ship1_mmsi']
    ship2_mmsi = scene_data['ship2_mmsi']
    change_time = scene_data['change_time']

    # 获取完整场景的时间范围
    full_scene = scene_data['full_scene']
    scene_times = np.unique(full_scene[:, 0])
    scene_start_time = int(scene_times[0])
    scene_end_time = int(scene_times[-1])

    # 扩展时间范围以显示更多上下文
    extended_start = max(scene_start_time - 300, 0)  # 提前5分钟
    extended_end = scene_end_time + 300  # 延后5分钟

    # 提取两船在扩展时间范围内的轨迹
    ship1_data = tras_df[(tras_df['MMSI'] == ship1_mmsi) &
                         (tras_df['PosTime'] >= extended_start) &
                         (tras_df['PosTime'] <= extended_end)].sort_values('PosTime')

    ship2_data = tras_df[(tras_df['MMSI'] == ship2_mmsi) &
                         (tras_df['PosTime'] >= extended_start) &
                         (tras_df['PosTime'] <= extended_end)].sort_values('PosTime')

    # 找到两船都有数据的时间点
    ship1_times = set(ship1_data['PosTime'])
    ship2_times = set(ship2_data['PosTime'])
    common_times = sorted(list(ship1_times & ship2_times))

    # 构建轨迹数据
    extracted_trajectories = []
    for time in common_times:
        ship1_point = ship1_data[ship1_data['PosTime'] == time].iloc[0]
        ship2_point = ship2_data[ship2_data['PosTime'] == time].iloc[0]

        # 转换为与原始格式兼容的数据结构
        ship1_array = np.array([
            int(time),
            int(ship1_mmsi),
            float(ship1_point['Lon']),
            float(ship1_point['Lat']),
            float(ship1_point['Cog']),
            float(ship1_point['Sog'])
        ])

        ship2_array = np.array([
            int(time),
            int(ship2_mmsi),
            float(ship2_point['Lon']),
            float(ship2_point['Lat']),
            float(ship2_point['Cog']),
            float(ship2_point['Sog'])
        ])

        extracted_trajectories.append(np.array([ship1_array, ship2_array]))

    return extracted_trajectories, change_time


def visualize_overtaking_success(extracted_trajectories, change_time, scene_id,
                                 overtaking_ship, overtaken_ship, save_gif=False, save_image=False):
    """
    可视化追越成功场景
    
    :param extracted_trajectories: 提取的轨迹数据
    :param change_time: 位置变化时刻
    :param scene_id: 场景编号
    :param overtaking_ship: 追越船MMSI
    :param overtaken_ship: 被追越船MMSI
    :param save_gif: 是否保存动画
    :param save_image: 是否保存静态图片
    """
    if not extracted_trajectories:
        print(f"场景 {scene_id} 没有有效的轨迹数据")
        return

    # 计算所有轨迹点的范围
    all_x = []
    all_y = []
    for frame in extracted_trajectories:
        for data in frame:
            all_x.append(data[2])  # 经度
            all_y.append(data[3])  # 纬度

    if not all_x or not all_y:
        print(f"场景 {scene_id} 没有有效的坐标数据")
        return

    x_min, x_max = min(all_x), max(all_x)
    y_min, y_max = min(all_y), max(all_y)

    # 设置图形
    fig, ax = plt.subplots(figsize=(12, 10))
    ax.set_xlim(x_min - 0.001, x_max + 0.001)
    ax.set_ylim(y_min - 0.001, y_max + 0.001)
    ax.set_xlabel("Longitude")
    ax.set_ylabel("Latitude")
    ax.set_title(f"Overtaking Success Scene {scene_id}\n"
                 f"Overtaking Ship: {overtaking_ship}, Overtaken Ship: {overtaken_ship}")

    # 设置颜色
    overtaking_color = 'red'
    overtaken_color = 'blue'

    # 初始化轨迹线
    overtaking_line, = ax.plot([], [], '-', color=overtaking_color, linewidth=2,
                               label=f'Overtaking Ship {overtaking_ship}')
    overtaken_line, = ax.plot([], [], '-', color=overtaken_color, linewidth=2,
                              label=f'Overtaken Ship {overtaken_ship}')

    # 初始化散点
    overtaking_scatter = ax.scatter([], [], color=overtaking_color, s=100, zorder=5)
    overtaken_scatter = ax.scatter([], [], color=overtaken_color, s=100, zorder=5)

    # 位置变化标记
    change_marker = ax.scatter([], [], color='green', s=200, marker='*',
                               label='Position Change', zorder=6)

    ax.legend()
    ax.grid(True, alpha=0.3)

    # 轨迹数据
    overtaking_x, overtaking_y = [], []
    overtaken_x, overtaken_y = [], []

    def init():
        overtaking_x.clear()
        overtaking_y.clear()
        overtaken_x.clear()
        overtaken_y.clear()
        overtaking_line.set_data([], [])
        overtaken_line.set_data([], [])
        overtaking_scatter.set_offsets(np.empty((0, 2)))
        overtaken_scatter.set_offsets(np.empty((0, 2)))
        change_marker.set_offsets(np.empty((0, 2)))
        return overtaking_line, overtaken_line, overtaking_scatter, overtaken_scatter, change_marker

    def update(frame):
        current_data = extracted_trajectories[frame]
        current_time = int(current_data[0][0])

        # 更新标题显示当前时间
        ax.set_title(f"Overtaking Success Scene {scene_id} - Time: {current_time}\n"
                     f"Overtaking Ship: {overtaking_ship}, Overtaken Ship: {overtaken_ship}")

        # 提取两船数据
        ship1_data = current_data[0]  # 第一艘船
        ship2_data = current_data[1]  # 第二艘船

        # 确定哪艘是追越船，哪艘是被追越船
        if int(ship1_data[1]) == overtaking_ship:
            overtaking_data = ship1_data
            overtaken_data = ship2_data
        else:
            overtaking_data = ship2_data
            overtaken_data = ship1_data

        # 更新追越船轨迹
        overtaking_x.append(overtaking_data[2])
        overtaking_y.append(overtaking_data[3])
        overtaking_line.set_data(overtaking_x, overtaking_y)
        overtaking_scatter.set_offsets([[overtaking_data[2], overtaking_data[3]]])

        # 更新被追越船轨迹
        overtaken_x.append(overtaken_data[2])
        overtaken_y.append(overtaken_data[3])
        overtaken_line.set_data(overtaken_x, overtaken_y)
        overtaken_scatter.set_offsets([[overtaken_data[2], overtaken_data[3]]])

        # 标记位置变化时刻
        if current_time == change_time:
            change_marker.set_offsets([[overtaking_data[2], overtaking_data[3]]])

        return overtaking_line, overtaken_line, overtaking_scatter, overtaken_scatter, change_marker

    # 创建动画
    ani = FuncAnimation(
        fig, update, init_func=init, frames=len(extracted_trajectories),
        interval=200, blit=True, repeat=False
    )

    # 保存动画
    if save_gif:
        output_dir = Path("vis/overtaking_success_animations")
        output_dir.mkdir(parents=True, exist_ok=True)
        gif_path = output_dir / f"overtaking_success_{scene_id}.gif"
        ani.save(str(gif_path), writer="pillow", fps=5)
        print(f"动画已保存: {gif_path}")

    # 保存静态图片
    if save_image:
        output_dir = Path("vis/overtaking_success_images")
        output_dir.mkdir(parents=True, exist_ok=True)
        img_path = output_dir / f"overtaking_success_{scene_id}.png"
        plt.savefig(str(img_path), dpi=300, bbox_inches='tight')
        print(f"静态图片已保存: {img_path}")

    # plt.show()


def main():
    """主函数"""
    # 加载数据
    data_time = '2024_1'

    print("正在加载数据...")

    # 加载追越成功场景数据
    success_file = Path(f"result/plot_data/{data_time}/overtaking_success_scenes.pkl")
    if not success_file.exists():
        print(f"未找到追越成功场景文件: {success_file}")
        return

    with open(success_file, 'rb') as f:
        success_scenes = pickle.load(f)

    # 加载轨迹数据
    tras_df = pd.read_parquet(f'data/tras_{data_time}_inter.parquet')

    print(f"✅ 数据加载完成:")
    print(f"   追越成功场景: {len(success_scenes)} 个")
    print(f"   轨迹数据记录: {len(tras_df)} 条")

    if not success_scenes:
        print("没有追越成功场景数据")
        return

    # 创建可视化目录
    vis_dir = Path("vis")
    vis_dir.mkdir(exist_ok=True)

    # 2. 对所有场景进行详细可视化
    print("\n=== 详细可视化所有追越成功场景 ===")
    total_scenes = len(success_scenes)

    for i, scene in enumerate(tqdm(success_scenes, desc="可视化进度")):
        print(f"\n可视化场景 {i + 1}/{total_scenes}:")
        print(f"   追越船: {scene['overtaking_ship']}")
        print(f"   被追越船: {scene['overtaken_ship']}")
        print(f"   位置变化时刻: {scene['change_time']}")

        # 提取轨迹数据
        extracted_trajectories, change_time = extract_trajectories_from_overtaking_scene(scene, tras_df)

        if extracted_trajectories:
            # 可视化
            visualize_overtaking_success(
                extracted_trajectories,
                change_time,
                i + 1,
                scene['overtaking_ship'],
                scene['overtaken_ship'],
                save_gif=True,
                save_image=True
            )
        else:
            print(f"   场景 {i + 1} 没有有效的轨迹数据")

    print(f"\n✅ 可视化完成!")
    print(f"   处理场景数: {total_scenes}")
    print(f"   输出目录: vis/")
    print(f"   动画文件: vis/overtaking_success_animations/")
    print(f"   静态图片: vis/overtaking_success_images/")


if __name__ == '__main__':
    main()
