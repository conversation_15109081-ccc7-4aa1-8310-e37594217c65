"""
船舶领域拟合系统 - 基于扇区边界的椭圆拟合
基于概率密度分析的扇区边界结果，拟合船舶领域椭圆

核心功能：
1. 加载扇区边界结果
2. 基于扇区边界点拟合椭圆
3. 评估拟合质量
4. 可视化拟合结果
5. 保存椭圆参数

输入：sector_boundaries_results.pkl
输出：ship_domain_ellipse_params.pkl

使用方法：
python ship_domain_fitting.py
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from scipy.optimize import leastsq

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ShipDomainFitter:
    """船舶领域拟合器"""
    
    def __init__(self, debug=False):
        """
        初始化船舶领域拟合器
        
        :param debug: 是否开启调试模式
        """
        self.debug = debug
        
        # 拟合结果存储
        self.ellipse_params = {}
        self.fitting_quality = {}
        
        # 输入数据
        self.sector_boundaries = {}
        self.length_intervals = []
        
        print(f"🎯 船舶领域拟合器初始化完成")
    
    def load_sector_boundaries(self):
        """加载扇区边界结果"""
        print("\n=== 加载扇区边界结果 ===")
        
        result_file = Path("result/probability_density/sector_boundaries_results.pkl")
        
        if not result_file.exists():
            print(f"❌ 未找到扇区边界结果文件: {result_file}")
            print("请先运行 probability_density_analysis.py 生成扇区边界结果")
            return False
        
        with open(result_file, 'rb') as f:
            results = pickle.load(f)
        
        self.sector_boundaries = results['sector_boundaries']
        self.length_intervals = results['length_intervals']
        
        print(f"✅ 加载扇区边界结果: {result_file}")
        print(f"   场景数量: {len(self.sector_boundaries)}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")
        
        return True
    
    def fit_ship_domains(self):
        """拟合船舶领域 - 追越仅使用前方边界点"""
        print("\n=== 拟合船舶领域 ===")

        success_count = 0
        total_count = len(self.sector_boundaries)

        for key, boundaries in self.sector_boundaries.items():
            scenario_type, length_interval = key.split('_', 1)

            print(f"\n🎯 拟合 {scenario_type}-{length_interval} 的船舶领域...")

            # 转换边界点为坐标
            boundary_points = self._convert_boundaries_to_points(boundaries)

            # 追越避让的特殊处理：过滤前方边界点
            if scenario_type == "追越":
                boundary_points = self._filter_forward_boundary_points(boundary_points)
                print(f"   追越避让过滤后前方边界点: {len(boundary_points)}个")

            # 根据场景类型调整最小边界点要求
            min_points = 2 if scenario_type == "追越" else 3

            if len(boundary_points) < min_points:
                print(f"⚠️  {scenario_type}-{length_interval}: 边界点不足({len(boundary_points)}个)，跳过拟合")
                continue

            # 根据场景类型选择拟合方法
            if scenario_type == "追越":
                ellipse_result = self._fit_ellipse_for_overtaking(boundary_points)
            else:
                ellipse_result = self._fit_ellipse_from_boundaries(boundary_points)

            if ellipse_result is None:
                print(f"❌ {scenario_type}-{length_interval}: 椭圆拟合失败")
                continue

            # 评估拟合质量
            quality_metrics = self._evaluate_fitting_quality(boundary_points, ellipse_result)

            # 存储结果
            self.ellipse_params[key] = ellipse_result
            self.fitting_quality[key] = quality_metrics

            print(f"✅ {scenario_type}-{length_interval}: 椭圆拟合完成")
            print(f"   长半轴: {ellipse_result['a']:.1f}m, 短半轴: {ellipse_result['b']:.1f}m")
            print(f"   拟合质量: R²={quality_metrics['r_squared']:.3f}")
            print(f"   边界点数: {len(boundary_points)}, 平均误差: {quality_metrics['mean_error']:.3f}")

            success_count += 1

        print(f"\n✅ 船舶领域拟合完成: {success_count}/{total_count} 个场景拟合成功")
    
    def _convert_boundaries_to_points(self, boundaries):
        """将扇区边界转换为坐标点"""
        boundary_points = []
        
        for sector_name, boundary_info in boundaries.items():
            distance = boundary_info['boundary_distance']
            angle = boundary_info['boundary_angle']
            
            # 转换为坐标
            x = distance * np.cos(angle)
            y = distance * np.sin(angle)
            
            boundary_points.append((x, y, sector_name))
        
        return boundary_points

    def _filter_forward_boundary_points(self, boundary_points):
        """过滤前方边界点（仅用于追越避让）"""
        forward_points = []

        for x, y, sector_name in boundary_points:
            if y > 0:  # 只保留前方的边界点（Y > 0）
                forward_points.append((x, y, sector_name))
            else:
                print(f"   过滤掉后方边界点: {sector_name} ({x:.1f}, {y:.1f})")

        return forward_points

    def _fit_ellipse_for_overtaking(self, boundary_points):
        """追越避让的特殊椭圆拟合方法"""
        try:
            # 提取坐标
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            if len(x_coords) < 2:
                return None

            # 追越避让的特殊处理：基于前方边界点的分布特点
            if len(x_coords) >= 3:
                # 如果有足够的前方边界点，使用代数方法拟合
                result = self._fit_ellipse_algebraic(x_coords, y_coords)
                if result is not None:
                    result['fitting_method'] = 'overtaking_algebraic'
                    return result

            # 如果边界点较少，使用基于前方分布的估计方法
            result = self._fit_ellipse_forward_estimation(x_coords, y_coords)
            if result is not None:
                result['fitting_method'] = 'overtaking_forward_estimation'
                return result

            return None

        except Exception as e:
            if self.debug:
                print(f"追越椭圆拟合失败: {e}")
            return None

    def _fit_ellipse_forward_estimation(self, x_coords, y_coords):
        """基于前方分布的椭圆估计（追越专用）"""
        try:
            # 基于前方边界点估计椭圆参数
            x_range = np.max(x_coords) - np.min(x_coords)
            y_max = np.max(y_coords)
            y_min = max(0, np.min(y_coords))  # 确保不小于0
            y_range = y_max - y_min

            # 追越避让的椭圆特点：纵向较长，主要在前方
            # 长半轴（纵向）：基于前方最远点
            a = max(y_max * 1.2, 100.0)  # 长半轴，最小100米

            # 短半轴（横向）：基于横向分布
            b = max(x_range * 0.8, 50.0)  # 短半轴，最小50米

            # 确保椭圆形状合理（追越通常是纵向长椭圆）
            if a < b:  # 如果短半轴比长半轴大，调整
                a = max(b * 1.5, 150.0)

            return {
                'a': float(a),
                'b': float(b),
                'data_count': len(x_coords),
                'cleaned_count': len(x_coords),
                'forward_only': True
            }

        except Exception as e:
            if self.debug:
                print(f"前方估计失败: {e}")
            return None
    
    def _fit_ellipse_from_boundaries(self, boundary_points):
        """基于边界点拟合椭圆"""
        try:
            # 提取坐标
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            if len(x_coords) < 3:
                return None

            # 方法1：直接代数方法拟合
            ellipse_result = self._fit_ellipse_algebraic(x_coords, y_coords)

            if ellipse_result is not None:
                return ellipse_result

            # 方法2：简单估计（备选）
            ellipse_result = self._fit_ellipse_simple_estimation(x_coords, y_coords)

            return ellipse_result

        except Exception as e:
            if self.debug:
                print(f"椭圆拟合失败: {e}")
            return None

    def _fit_ellipse_algebraic(self, x_coords, y_coords):
        """直接代数方法拟合椭圆（不需要初始参数）"""
        try:
            # 数据预处理
            cleaned_x, cleaned_y = self._preprocess_boundary_data(x_coords, y_coords)

            if len(cleaned_x) < 3:
                return None

            # 数据中心化，提高数值稳定性
            x_mean = np.mean(cleaned_x)
            y_mean = np.mean(cleaned_y)
            x_centered = cleaned_x - x_mean
            y_centered = cleaned_y - y_mean

            # 数据归一化，进一步提高数值稳定性
            x_scale = np.std(x_centered) if np.std(x_centered) > 0 else 1.0
            y_scale = np.std(y_centered) if np.std(y_centered) > 0 else 1.0
            x_norm = x_centered / x_scale
            y_norm = y_centered / y_scale

            # 椭圆的一般方程：Ax² + Bxy + Cy² + Dx + Ey + F = 0
            # 对于轴对齐椭圆，假设B=0：Ax² + Cy² + Dx + Ey + F = 0

            # 构建设计矩阵
            D = np.column_stack([
                x_norm**2,           # A系数
                y_norm**2,           # C系数
                x_norm,              # D系数
                y_norm,              # E系数
                np.ones(len(x_norm)) # F系数
            ])

            # 使用SVD求解齐次线性方程组 D * coeffs = 0
            # 寻找最小二乘解
            try:
                U, s, Vt = np.linalg.svd(D)
                coeffs = Vt[-1, :]  # 最小奇异值对应的解
            except np.linalg.LinAlgError:
                # SVD失败，尝试特征值分解
                DtD = D.T @ D
                eigenvals, eigenvecs = np.linalg.eigh(DtD)
                coeffs = eigenvecs[:, 0]  # 最小特征值对应的特征向量

            A, C, D_coeff, E_coeff, F = coeffs

            # 确保A和C为正（椭圆条件）
            if A <= 0 or C <= 0:
                # 如果系数为负，取绝对值
                A, C = abs(A), abs(C)

            # 避免除零错误
            if abs(A) < 1e-10 or abs(C) < 1e-10:
                return None

            # 计算椭圆中心（归一化坐标系中）
            x0_norm = -D_coeff / (2 * A) if abs(A) > 1e-10 else 0
            y0_norm = -E_coeff / (2 * C) if abs(C) > 1e-10 else 0

            # 计算椭圆参数
            # 椭圆方程：A(x-x0)² + C(y-y0)² = constant
            constant = A * x0_norm**2 + C * y0_norm**2 - F

            if constant <= 0:
                # 如果常数项不合理，使用数据分布估计
                return self._fit_ellipse_simple_estimation(cleaned_x, cleaned_y)

            # 计算半轴长度（归一化坐标系中）
            a_norm = np.sqrt(constant / C)  # 纵向半轴
            b_norm = np.sqrt(constant / A)  # 横向半轴

            # 转换回原始坐标系
            a_original = a_norm * y_scale
            b_original = b_norm * x_scale

            # 验证结果的合理性
            if not self._validate_ellipse_parameters(a_original, b_original, cleaned_x, cleaned_y):
                return self._fit_ellipse_simple_estimation(cleaned_x, cleaned_y)

            return {
                'a': float(a_original),
                'b': float(b_original),
                'data_count': len(x_coords),
                'cleaned_count': len(cleaned_x),
                'fitting_method': 'algebraic_direct',
                'center_x': float(x_mean),  # 椭圆中心（原始坐标系）
                'center_y': float(y_mean)
            }

        except Exception as e:
            if self.debug:
                print(f"代数方法拟合失败: {e}")
            # 回退到简单估计方法
            return self._fit_ellipse_simple_estimation(x_coords, y_coords)

    def _validate_ellipse_parameters(self, a, b, x_coords, y_coords):
        """验证椭圆参数的合理性"""
        try:
            # 基本尺寸检查
            if a <= 0 or b <= 0:
                return False

            if a < 15 or b < 10:  # 最小尺寸
                return False

            if a > 3000 or b > 2000:  # 最大尺寸
                return False

            # 长短轴比例检查
            ratio = max(a, b) / min(a, b)
            if ratio > 15:  # 比例过大
                return False

            # 与数据分布的一致性检查
            distances = np.sqrt(x_coords**2 + y_coords**2)
            max_data_distance = np.max(distances)
            ellipse_max_radius = max(a, b)

            # 椭圆不应该比数据分布小太多或大太多
            if ellipse_max_radius < max_data_distance * 0.3:
                return False
            if ellipse_max_radius > max_data_distance * 3.0:
                return False

            return True

        except Exception:
            return False

    def _fit_ellipse_least_squares(self, x_coords, y_coords):
        """改进的鲁棒椭圆拟合"""
        try:
            # 1. 数据预处理和清理
            cleaned_x, cleaned_y = self._preprocess_boundary_data(x_coords, y_coords)

            if len(cleaned_x) < 3:
                return None

            # 2. 多种初始参数估计策略
            initial_params_list = self._generate_initial_parameters(cleaned_x, cleaned_y)

            best_result = None
            best_residual = float('inf')

            # 3. 尝试多种初始参数
            for a_init, b_init in initial_params_list:
                try:
                    result = self._fit_with_constraints(cleaned_x, cleaned_y, a_init, b_init)

                    if result is not None:
                        # 计算拟合残差
                        residual = self._calculate_fitting_residual(cleaned_x, cleaned_y, result)

                        if residual < best_residual:
                            best_residual = residual
                            best_result = result

                except Exception:
                    continue

            # 4. 形状合理性验证
            if best_result is not None and self._validate_ellipse_shape(best_result, cleaned_x, cleaned_y):
                best_result.update({
                    'data_count': len(x_coords),
                    'cleaned_count': len(cleaned_x),
                    'fitting_method': 'robust_least_squares',
                    'fitting_residual': best_residual
                })
                return best_result

            return None

        except Exception as e:
            if self.debug:
                print(f"鲁棒椭圆拟合失败: {e}")
            return None

    def _preprocess_boundary_data(self, x_coords, y_coords):
        """预处理边界数据"""
        try:
            # 移除重复点
            points = np.column_stack([x_coords, y_coords])
            unique_points = np.unique(points, axis=0)

            if len(unique_points) < len(points):
                x_coords = unique_points[:, 0]
                y_coords = unique_points[:, 1]

            # 异常值检测和移除
            distances = np.sqrt(x_coords**2 + y_coords**2)

            # 使用改进的异常值检测
            Q1, Q3 = np.percentile(distances, [25, 75])
            IQR = Q3 - Q1

            # 更宽松的异常值边界
            lower_bound = Q1 - 2.0 * IQR
            upper_bound = Q3 + 2.0 * IQR

            valid_mask = (distances >= max(lower_bound, 10)) & (distances <= min(upper_bound, 3000))

            return x_coords[valid_mask], y_coords[valid_mask]

        except Exception as e:
            if self.debug:
                print(f"数据预处理失败: {e}")
            return x_coords, y_coords

    def _generate_initial_parameters(self, x_coords, y_coords):
        """生成多种初始参数估计"""
        try:
            initial_params = []

            # 策略1：基于数据范围
            x_range = np.max(x_coords) - np.min(x_coords)
            y_range = np.max(y_coords) - np.min(y_coords)

            a1 = max(y_range * 0.6, 50)
            b1 = max(x_range * 0.6, 30)
            initial_params.append((a1, b1))

            # 策略2：基于数据分布的标准差
            a2 = max(np.std(y_coords) * 2, 50)
            b2 = max(np.std(x_coords) * 2, 30)
            initial_params.append((a2, b2))

            # 策略3：基于95%分位数
            distances = np.sqrt(x_coords**2 + y_coords**2)
            p95_dist = np.percentile(distances, 95)

            a3 = max(p95_dist * 0.8, 50)
            b3 = max(p95_dist * 0.6, 30)
            initial_params.append((a3, b3))

            # 策略4：基于最大值
            a4 = max(abs(np.max(y_coords)), abs(np.min(y_coords)), 50)
            b4 = max(abs(np.max(x_coords)), abs(np.min(x_coords)), 30)
            initial_params.append((a4, b4))

            return initial_params

        except Exception as e:
            if self.debug:
                print(f"初始参数生成失败: {e}")
            return [(100, 50)]  # 默认参数

    def _fit_with_constraints(self, x_coords, y_coords, a_init, b_init):
        """带约束的椭圆拟合"""
        try:
            from scipy.optimize import minimize

            def objective(params):
                a, b = abs(params[0]), abs(params[1])
                if a <= 0 or b <= 0:
                    return 1e6

                # 椭圆方程残差
                residuals = (x_coords / b) ** 2 + (y_coords / a) ** 2 - 1
                return np.sum(residuals ** 2)

            # 约束条件
            constraints = [
                {'type': 'ineq', 'fun': lambda params: params[0] - 20},  # a >= 20
                {'type': 'ineq', 'fun': lambda params: params[1] - 15},  # b >= 15
                {'type': 'ineq', 'fun': lambda params: 2000 - params[0]},  # a <= 2000
                {'type': 'ineq', 'fun': lambda params: 1500 - params[1]},  # b <= 1500
            ]

            result = minimize(
                objective,
                [a_init, b_init],
                method='SLSQP',
                constraints=constraints,
                options={'maxiter': 1000}
            )

            if result.success:
                a, b = abs(result.x[0]), abs(result.x[1])
                return {'a': float(a), 'b': float(b)}

            return None

        except Exception as e:
            if self.debug:
                print(f"约束拟合失败: {e}")
            return None

    def _calculate_fitting_residual(self, x_coords, y_coords, ellipse_params):
        """计算拟合残差"""
        try:
            a, b = ellipse_params['a'], ellipse_params['b']
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2
            residuals = np.abs(ellipse_values - 1)
            return np.mean(residuals)
        except:
            return float('inf')

    def _validate_ellipse_shape(self, ellipse_params, x_coords, y_coords):
        """验证椭圆形状的合理性"""
        try:
            a, b = ellipse_params['a'], ellipse_params['b']

            # 1. 基本尺寸检查
            if a < 20 or b < 15 or a > 2000 or b > 1500:
                return False

            # 2. 长短轴比例检查
            ratio = max(a, b) / min(a, b)
            if ratio > 10:  # 比例过大，可能是退化椭圆
                return False

            # 3. 与数据分布的一致性检查
            distances = np.sqrt(x_coords**2 + y_coords**2)
            max_data_distance = np.max(distances)
            ellipse_max_radius = max(a, b)

            # 椭圆不应该比数据分布小太多或大太多
            if ellipse_max_radius < max_data_distance * 0.5:
                return False
            if ellipse_max_radius > max_data_distance * 2.0:
                return False

            # 4. 数据点在椭圆内外的分布检查
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2
            inside_count = np.sum(ellipse_values <= 1.2)  # 允许一些容差
            outside_count = len(ellipse_values) - inside_count

            # 大部分点应该在椭圆附近
            if inside_count < len(ellipse_values) * 0.3:
                return False

            return True

        except Exception as e:
            if self.debug:
                print(f"椭圆形状验证失败: {e}")
            return False
    
    def _fit_ellipse_simple_estimation(self, x_coords, y_coords):
        """简单估计椭圆参数"""
        try:
            # 基于数据范围估计椭圆参数
            x_range = np.max(x_coords) - np.min(x_coords)
            y_range = np.max(y_coords) - np.min(y_coords)
            
            # 使用数据范围的一定比例作为椭圆参数
            a = y_range * 0.6  # 纵向半轴
            b = x_range * 0.6  # 横向半轴
            
            if a > 0 and b > 0:
                return {
                    'a': float(a),
                    'b': float(b),
                    'data_count': len(x_coords),
                    'cleaned_count': len(x_coords),
                    'fitting_method': 'simple_estimation'
                }
            
            return None
            
        except Exception as e:
            if self.debug:
                print(f"简单估计失败: {e}")
            return None
    
    def _evaluate_fitting_quality(self, boundary_points, ellipse_result):
        """改进的多维度拟合质量评估"""
        try:
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])

            a, b = ellipse_result['a'], ellipse_result['b']

            # 1. 几何拟合误差
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2
            geometric_errors = np.abs(ellipse_values - 1)

            # 2. 改进的R²计算
            r_squared = self._calculate_improved_r_squared(ellipse_values)

            # 3. 拟合一致性评估
            consistency_score = self._calculate_consistency_score(x_coords, y_coords, a, b)

            # 4. 椭圆形状合理性评估
            shape_score = self._calculate_shape_reasonableness(a, b, x_coords, y_coords)

            # 5. 数据覆盖度评估
            coverage_score = self._calculate_coverage_score(x_coords, y_coords, a, b)

            # 6. 综合质量分数
            overall_quality = (
                r_squared * 0.3 +
                consistency_score * 0.25 +
                shape_score * 0.25 +
                coverage_score * 0.2
            )

            return {
                'r_squared': float(r_squared),
                'consistency_score': float(consistency_score),
                'shape_score': float(shape_score),
                'coverage_score': float(coverage_score),
                'overall_quality': float(overall_quality),
                'mean_error': float(np.mean(geometric_errors)),
                'max_error': float(np.max(geometric_errors)),
                'std_error': float(np.std(geometric_errors)),
                'point_count': len(boundary_points),
                'ellipse_ratio': float(max(a, b) / min(a, b))
            }

        except Exception as e:
            if self.debug:
                print(f"质量评估失败: {e}")
            return self._get_default_quality_metrics(len(boundary_points))

    def _calculate_improved_r_squared(self, ellipse_values):
        """改进的R²计算"""
        try:
            # 理想椭圆值应该为1
            ideal_values = np.ones_like(ellipse_values)

            # 计算残差平方和
            ss_res = np.sum((ellipse_values - ideal_values) ** 2)

            # 计算总平方和（相对于均值）
            mean_value = np.mean(ellipse_values)
            ss_tot = np.sum((ellipse_values - mean_value) ** 2)

            if ss_tot > 1e-10:
                r_squared = 1 - (ss_res / ss_tot)
            else:
                # 如果方差很小，说明拟合很好
                mean_error = np.mean(np.abs(ellipse_values - 1))
                r_squared = max(0, 1 - mean_error * 2)

            return max(0, min(1, r_squared))

        except:
            return 0.0

    def _calculate_consistency_score(self, x_coords, y_coords, a, b):
        """计算拟合一致性分数"""
        try:
            # 计算每个点到椭圆边界的相对距离
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2
            relative_distances = np.abs(ellipse_values - 1)

            # 一致性基于距离的变异系数
            mean_distance = np.mean(relative_distances)
            std_distance = np.std(relative_distances)

            if mean_distance > 0:
                cv = std_distance / mean_distance
                consistency_score = max(0, 1 - cv)
            else:
                consistency_score = 1.0

            return consistency_score

        except:
            return 0.0

    def _calculate_shape_reasonableness(self, a, b, x_coords, y_coords):
        """计算椭圆形状合理性分数"""
        try:
            score = 1.0

            # 1. 长短轴比例合理性
            ratio = max(a, b) / min(a, b)
            if ratio > 5:
                score *= max(0, 1 - (ratio - 5) / 10)

            # 2. 尺寸合理性
            data_range = np.max(np.sqrt(x_coords**2 + y_coords**2))
            ellipse_size = max(a, b)

            size_ratio = ellipse_size / data_range if data_range > 0 else 1
            if size_ratio < 0.5 or size_ratio > 2.0:
                score *= 0.7

            # 3. 最小尺寸检查
            if min(a, b) < 20:
                score *= 0.5

            return score

        except:
            return 0.0

    def _calculate_coverage_score(self, x_coords, y_coords, a, b):
        """计算数据覆盖度分数"""
        try:
            ellipse_values = (x_coords / b) ** 2 + (y_coords / a) ** 2

            # 计算在椭圆内、边界附近、外部的点数
            inside_count = np.sum(ellipse_values <= 0.8)
            boundary_count = np.sum((ellipse_values > 0.8) & (ellipse_values <= 1.2))
            outside_count = np.sum(ellipse_values > 1.2)

            total_points = len(ellipse_values)

            # 理想情况：大部分点在边界附近
            boundary_ratio = boundary_count / total_points
            inside_ratio = inside_count / total_points
            outside_ratio = outside_count / total_points

            # 覆盖度分数：边界附近的点越多越好
            coverage_score = boundary_ratio + inside_ratio * 0.5 + outside_ratio * 0.3

            return min(1.0, coverage_score)

        except:
            return 0.0

    def _get_default_quality_metrics(self, point_count):
        """获取默认质量指标"""
        return {
            'r_squared': 0.0,
            'consistency_score': 0.0,
            'shape_score': 0.0,
            'coverage_score': 0.0,
            'overall_quality': 0.0,
            'mean_error': float('inf'),
            'max_error': float('inf'),
            'std_error': float('inf'),
            'point_count': point_count,
            'ellipse_ratio': 0.0
        }

    def visualize_fitting_results(self):
        """可视化拟合结果"""
        print("\n=== 生成可视化结果 ===")

        vis_dir = Path("vis/test/ship_domain_fitting")
        vis_dir.mkdir(parents=True, exist_ok=True)

        for key, ellipse_params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)

            # 创建拟合结果图
            self._create_fitting_plot(key, scenario_type, length_interval, vis_dir)

        # 创建对比分析图
        self._create_comparison_plot(vis_dir)

        print(f"✅ 可视化结果保存至: {vis_dir}")

    def _create_fitting_plot(self, key, scenario_type, length_interval, vis_dir):
        """创建单个场景的拟合结果图"""
        ellipse_params = self.ellipse_params[key]
        quality_metrics = self.fitting_quality[key]
        boundaries = self.sector_boundaries[key]

        # 创建图形
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(f'{scenario_type}避让 - {length_interval} 船舶领域拟合结果', fontsize=16)

        # 左图：拟合结果
        ax1 = axes[0]

        # 绘制边界点
        boundary_points = self._convert_boundaries_to_points(boundaries)
        x_coords = [p[0] for p in boundary_points]
        y_coords = [p[1] for p in boundary_points]

        ax1.scatter(x_coords, y_coords, c='red', s=50, alpha=0.8, label='扇区边界点')

        # 绘制拟合椭圆
        self._plot_ellipse(ax1, ellipse_params['a'], ellipse_params['b'])

        ax1.set_title('椭圆拟合结果')
        ax1.set_xlabel('横向距离 (m)')
        ax1.set_ylabel('纵向距离 (m)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        ax1.legend()

        # 右图：质量指标
        ax2 = axes[1]
        metrics = ['R²', '平均误差', '最大误差', '标准差']
        values = [
            quality_metrics['r_squared'],
            quality_metrics['mean_error'],
            quality_metrics['max_error'],
            quality_metrics['std_error']
        ]

        bars = ax2.bar(metrics, values, color=['green', 'orange', 'red', 'blue'])
        ax2.set_title('拟合质量指标')
        ax2.set_ylabel('数值')

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        # 保存图片
        filename = f"{scenario_type}_{length_interval}_fitting_result.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {key} 拟合图保存至: {save_path}")

    def _plot_ellipse(self, ax, a, b, center=(0, 0), color='blue', label='拟合椭圆'):
        """绘制椭圆"""
        theta = np.linspace(0, 2*np.pi, 100)
        x = center[0] + b * np.cos(theta)  # 横向半轴
        y = center[1] + a * np.sin(theta)  # 纵向半轴

        ax.plot(x, y, color=color, linewidth=2, label=label)

    def _create_comparison_plot(self, vis_dir):
        """创建对比分析图"""
        if len(self.ellipse_params) < 2:
            return

        # 收集数据
        comparison_data = []

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            quality = self.fitting_quality[key]

            comparison_data.append({
                'scenario_type': scenario_type,
                'length_interval': length_interval,
                'a': params['a'],
                'b': params['b'],
                'ratio': params['a'] / params['b'],
                'r_squared': quality['r_squared'],
                'point_count': quality['point_count']
            })

        df = pd.DataFrame(comparison_data)

        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('船舶领域椭圆参数对比分析', fontsize=16, fontweight='bold')

        # 1. 椭圆参数散点图
        ax1 = axes[0, 0]
        for scenario in df['scenario_type'].unique():
            scenario_data = df[df['scenario_type'] == scenario]
            ax1.scatter(scenario_data['b'], scenario_data['a'],
                       label=f'{scenario}避让', s=100, alpha=0.7)

        ax1.set_xlabel('短半轴 b (m)')
        ax1.set_ylabel('长半轴 a (m)')
        ax1.set_title('椭圆参数分布')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 长短轴比对比
        ax2 = axes[0, 1]
        if len(df['scenario_type'].unique()) > 1:
            df.boxplot(column='ratio', by='scenario_type', ax=ax2)
            ax2.set_title('长短轴比分布')
            ax2.set_xlabel('场景类型')
            ax2.set_ylabel('长短轴比')

        # 3. 不同船长区间的长半轴对比
        ax3 = axes[1, 0]
        if len(df) > 0:
            pivot_a = df.pivot_table(values='a', index='length_interval',
                                   columns='scenario_type', aggfunc='mean')
            pivot_a.plot(kind='bar', ax=ax3, rot=45)
            ax3.set_title('不同船长区间的长半轴对比')
            ax3.set_xlabel('船长区间')
            ax3.set_ylabel('长半轴 a (m)')
            ax3.legend(title='场景类型')

        # 4. 拟合质量对比
        ax4 = axes[1, 1]
        if len(df) > 0:
            pivot_r2 = df.pivot_table(values='r_squared', index='length_interval',
                                    columns='scenario_type', aggfunc='mean')
            pivot_r2.plot(kind='bar', ax=ax4, rot=45)
            ax4.set_title('拟合质量(R²)对比')
            ax4.set_xlabel('船长区间')
            ax4.set_ylabel('R²')
            ax4.legend(title='场景类型')

        plt.tight_layout()

        # 保存对比图
        save_path = vis_dir / "ellipse_parameters_comparison.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 对比分析图保存至: {save_path}")

    def save_results(self):
        """保存拟合结果"""
        print("\n=== 保存拟合结果 ===")

        result_dir = Path("result/ship_domain_fitting")
        result_dir.mkdir(parents=True, exist_ok=True)

        # 按场景类型分组
        crossing_params = {}
        overtaking_params = {}

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            if scenario_type == "交叉":
                crossing_params[length_interval] = params
            elif scenario_type == "追越":
                overtaking_params[length_interval] = params

        # 保存主结果文件
        results = {
            'crossing_ellipse_params': crossing_params,
            'overtaking_ellipse_params': overtaking_params,
            'length_intervals': self.length_intervals,
            'fitting_quality': self.fitting_quality,
            'all_ellipse_params': self.ellipse_params
        }

        result_file = result_dir / "ship_domain_ellipse_params.pkl"
        with open(result_file, 'wb') as f:
            pickle.dump(results, f)

        # 保存CSV结果
        self._save_csv_results(result_dir)

        print(f"✅ 拟合结果保存至: {result_file}")

        return result_file

    def _save_csv_results(self, result_dir):
        """保存CSV结果"""
        data = []

        for key, params in self.ellipse_params.items():
            scenario_type, length_interval = key.split('_', 1)
            quality = self.fitting_quality[key]

            data.append({
                'scenario_type': f'{scenario_type}避让',
                'length_interval': length_interval,
                'a_longitudinal_m': round(params['a'], 1),
                'b_lateral_m': round(params['b'], 1),
                'ratio_a_b': round(params['a'] / params['b'], 2),
                'r_squared': round(quality['r_squared'], 3),
                'mean_error': round(quality['mean_error'], 3),
                'boundary_points': quality['point_count'],
                'fitting_method': params.get('fitting_method', 'unknown')
            })

        if data:
            df = pd.DataFrame(data)
            csv_file = result_dir / "ship_domain_ellipse_parameters.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"   CSV文件: {csv_file}")

    def run_full_fitting(self):
        """运行完整的船舶领域拟合"""
        print("🎯 开始船舶领域拟合...")
        print("=" * 60)

        try:
            # 执行拟合流程
            if not self.load_sector_boundaries():
                return False

            self.fit_ship_domains()
            self.visualize_fitting_results()
            result_file = self.save_results()

            print("\n" + "=" * 60)
            print("🎉 船舶领域拟合完成！")
            print(f"📁 主要输出:")
            print(f"   椭圆参数: {result_file}")
            print(f"   可视化图: vis/ship_domain_fitting/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 船舶领域拟合失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数"""
    print(f"🎯 船舶领域拟合系统")
    print(f"   输入文件: sector_boundaries_results.pkl")
    print(f"   输出文件: ship_domain_ellipse_params.pkl")

    # 创建拟合器
    fitter = ShipDomainFitter(debug=False)

    # 运行完整拟合
    success = fitter.run_full_fitting()

    if success:
        print(f"\n📁 主要输出文件:")
        print(f"   椭圆参数: result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        print(f"   参数表格: result/ship_domain_fitting/ship_domain_ellipse_parameters.csv")
        print(f"   可视化图: vis/ship_domain_fitting/")
    else:
        print(f"\n❌ 拟合失败")


if __name__ == '__main__':
    main()
