#%% md
## 1. 距离统计 
#%%
import os

import matplotlib.pyplot as plt
from scipy import stats

plt.rcParams['font.sans-serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题


class DistanceVisualization:
    """距离分布可视化分析器"""

    def __init__(self, output_dir="vis0"):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        plt.style.use('seaborn-v0_8')

        # 颜色配置
        self.colors = {
            'crossing': '#FF6B6B',  # 红色系 - 交叉会遇
            'overtaking': '#4ECDC4',  # 蓝绿色系 - 追越会遇
            'fit_line': '#2C3E50'  # 深色 - 拟合线
        }

    def load_statistics_data(self, results_file=None):
        """
        加载统计分析结果
        
        Args:
            results_file: 结果文件路径，默认为number_statistics_results.pkl
            
        Returns:
            dict: 统计结果数据
        """
        if results_file is None:
            results_file = Path("result0") / "number_statistics_results.pkl"

        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"成功加载统计结果: {results_file}")
            return results
        except FileNotFoundError:
            print(f"错误: 未找到结果文件 {results_file}")
            print("请先运行 02.number_statistics.py 生成统计结果")
            return None
        except Exception as e:
            print(f"加载结果文件时出错: {e}")
            return None

    def fit_distribution(self, data, distribution_types=None):
        """
        拟合数据的概率分布
        
        Args:
            data: 数据数组
            distribution_types: 要尝试的分布类型列表
            
        Returns:
            dict: 最佳拟合结果
        """
        if distribution_types is None:
            distribution_types = [
                stats.norm,  # 正态分布
                stats.gamma,  # 伽马分布
                stats.lognorm,  # 对数正态分布
                stats.expon,  # 指数分布
                stats.weibull_min  # 威布尔分布
            ]

        best_fit = {
            'distribution': None,
            'params': None,
            'aic': np.inf,
            'name': 'normal'
        }

        for dist in distribution_types:
            try:
                # 拟合分布参数
                params = dist.fit(data)

                # 计算AIC (Akaike Information Criterion)
                log_likelihood = np.sum(dist.logpdf(data, *params))
                k = len(params)  # 参数数量
                aic = 2 * k - 2 * log_likelihood

                # 选择AIC最小的分布
                if aic < best_fit['aic']:
                    best_fit = {
                        'distribution': dist,
                        'params': params,
                        'aic': aic,
                        'name': dist.name
                    }
            except Exception as e:
                print(f"拟合 {dist.name} 分布时出错: {e}")
                continue

        return best_fit

    def create_distance_distribution_plot(self, distances, encounter_type, distance_type,
                                          bins=20, figsize=(12, 8)):
        """
        创建距离分布图（频率直方图 + 拟合曲线）
        """
        if len(distances) == 0:
            print(f"警告: {encounter_type} {distance_type} 距离数据为空")
            return None

        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)

        # 设置背景为白色，移除填充
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        # 智能生成均匀的bins边界
        data_range = max(distances) - min(distances)
        min_val = min(distances)
        max_val = max(distances)

        # 根据数据范围选择合适的步长
        if data_range <= 100:
            step = 5
        elif data_range <= 500:
            step = 25
        elif data_range <= 1000:
            step = 50
        else:
            step = 100

        # 生成均匀的bins边界
        start_bin = int(min_val // step) * step
        end_bin = int(max_val // step + 1) * step
        bins_edges = np.arange(start_bin, end_bin + step, step)

        # 确保bins范围覆盖所有数据
        if bins_edges[0] > min_val:
            bins_edges = np.insert(bins_edges, 0, bins_edges[0] - step)
        if bins_edges[-1] < max_val:
            bins_edges = np.append(bins_edges, bins_edges[-1] + step)

        # 使用预定义的bins_edges绘制直方图
        n, bins_edges, patches = ax.hist(distances, bins=bins_edges, density=False,
                                         weights=np.ones(len(distances)) / len(distances),
                                         alpha=0.7, color=self.colors[encounter_type],
                                         edgecolor='white', linewidth=1.2)

        # 设置x轴刻度：选择合适间隔的bins边界作为刻度
        tick_interval = max(1, len(bins_edges) // 8)  # 最多8个刻度
        selected_ticks = bins_edges[::tick_interval]

        ax.set_xticks(selected_ticks)
        ax.set_xticklabels([f'{tick:.0f}' for tick in selected_ticks], rotation=45)

        best_fit = self.fit_distribution(distances)

        if best_fit['distribution'] is not None:
            # 生成拟合曲线的x值
            x_range = np.linspace(min(distances), max(distances), 1000)

            # 计算拟合的概率密度
            fitted_pdf = best_fit['distribution'].pdf(x_range, *best_fit['params'])

            # 调整拟合曲线的尺度以匹配频率显示
            bin_width = step  # 使用固定的bin宽度
            fitted_pdf = fitted_pdf * bin_width

            # 绘制拟合曲线
            ax.plot(x_range, fitted_pdf, color=self.colors['fit_line'],
                    linewidth=2, alpha=0.9)

        # 设置网格：灰色虚线
        ax.grid(True, linestyle='--', color='gray', alpha=0.7, linewidth=0.8)

        # 设置坐标轴刻度线：黑色实线
        ax.tick_params(axis='both', which='major', color='black',
                       length=6, width=1, direction='in')
        ax.tick_params(axis='both', which='minor', color='black',
                       length=3, width=0.5, direction='in')

        # 设置坐标轴字体为 Times New Roman
        for tick in ax.get_xticklabels():
            tick.set_fontname('Times New Roman')
        for tick in ax.get_yticklabels():
            tick.set_fontname('Times New Roman')

        # 设置所有坐标轴边框：黑色实线，保留四条边
        for spine in ax.spines.values():
            spine.set_color('black')
            spine.set_linewidth(1)
            spine.set_visible(True)

        return fig

    def analyze_and_visualize_distances(self, results_data):
        """
        分析并可视化所有距离分布
        
        Args:
            results_data: 统计结果数据
            
        Returns:
            dict: 生成的图表信息
        """
        if not results_data:
            return None

        print("=== 开始距离分布可视化分析 ===")

        # 提取距离数据
        cross_distances = results_data['cross_related_encounters']
        non_cross_distances = results_data['non_cross_encounters']

        generated_plots = {}

        # 1. 交叉会遇最近距离分布
        if cross_distances.get('min_distances'):
            print("\n1. 分析交叉会遇最近距离分布...")
            fig1 = self.create_distance_distribution_plot(
                cross_distances['min_distances'], 'crossing', 'min'
            )
            if fig1:
                output_file1 = self.output_dir / 'crossing_min_distance_distribution.png'
                fig1.savefig(output_file1, dpi=300, bbox_inches='tight')
                print(f"图表已保存: {output_file1}")
                generated_plots['crossing_min'] = output_file1
                plt.close(fig1)

        # 2. 追越会遇最近距离分布
        if non_cross_distances.get('min_distances'):
            print("\n2. 分析追越会遇最近距离分布...")
            fig2 = self.create_distance_distribution_plot(
                non_cross_distances['min_distances'], 'overtaking', 'min'
            )
            if fig2:
                output_file2 = self.output_dir / 'overtaking_min_distance_distribution.png'
                fig2.savefig(output_file2, dpi=300, bbox_inches='tight')
                print(f"   图表已保存: {output_file2}")
                generated_plots['overtaking_min'] = output_file2
                plt.close(fig2)

        return generated_plots

    def generate_summary_report(self, results_data, generated_plots):
        """
        生成可视化摘要报告
        
        Args:
            results_data: 统计结果数据
            generated_plots: 生成的图表信息
        """
        cross_distances = results_data['cross_related_encounters']
        non_cross_distances = results_data['non_cross_encounters']

        print("\n" + "=" * 60)
        print("距离分布可视化分析摘要")
        print("=" * 60)

        print(f"数据概况:")
        if cross_distances.get('min_distances'):
            print(f"  交叉会遇样本: {len(cross_distances['min_distances'])} 个")
        if non_cross_distances.get('min_distances'):
            print(f"  追越会遇样本: {len(non_cross_distances['min_distances'])} 个")

        print(f"\n生成的图表: {len(generated_plots)} 张")
        print(f"\n图表说明:")
        print(f"  纵轴：频率（各区间数量占总数的比值，所有柱子高度之和=1）")
        print(f"  拟合曲线：已调整尺度以匹配频率显示")

        # 统计特征对比
        if (cross_distances.get('min_distances') and non_cross_distances.get('min_distances')):
            print(f"\n最近距离统计对比:")
            cross_min = cross_distances['min_distances']
            over_min = non_cross_distances['min_distances']
            print(f"  交叉会遇 - 均值: {np.mean(cross_min):.1f}m, 标准差: {np.std(cross_min):.1f}m")
            print(f"  追越会遇 - 均值: {np.mean(over_min):.1f}m, 标准差: {np.std(over_min):.1f}m")

        if (cross_distances.get('start_distances') and non_cross_distances.get('start_distances')):
            print(f"\n开始距离统计对比:")
            cross_start = cross_distances['start_distances']
            over_start = non_cross_distances['start_distances']
            print(f"  交叉会遇 - 均值: {np.mean(cross_start):.1f}m, 标准差: {np.std(cross_start):.1f}m")
            print(f"  追越会遇 - 均值: {np.mean(over_start):.1f}m, 标准差: {np.std(over_start):.1f}m")


def main_visualization():
    """
    主要可视化分析函数
    
    Returns:
        dict: 可视化结果摘要
    """
    print("=== 开始距离分布可视化分析 ===")

    # 初始化可视化器
    visualizer = DistanceVisualization()

    # 加载统计数据
    results_data = visualizer.load_statistics_data()
    if not results_data:
        return None

    # 生成主要分布图表
    generated_plots = visualizer.analyze_and_visualize_distances(results_data)

    # 生成摘要报告
    visualizer.generate_summary_report(results_data, generated_plots)

    print(f"\n=== 距离分布可视化完成 ===")
    print(f"图表保存位置: vis0/")
    print(f"  - 频率分布图: {len(generated_plots)}张")

    return {
        'individual_plots': generated_plots,
        'total_plots': len(generated_plots)
    }


if __name__ == '__main__':
    # 检查依赖文件
    results_file = "result0/number_statistics_results.pkl"
    if not os.path.exists(results_file):
        print("错误: 未找到统计结果文件")
        print(f"请先运行 02.number_statistics.py 生成 {results_file}")
        exit(1)

    # 执行可视化分析
    visualization_results = main_visualization()

    if visualization_results:
        print(f"\n✅ 可视化分析完成！")
        print(f"   生成图表总数: {visualization_results['total_plots']}")
        print(f"   查看 vis0/ 目录中的PNG文件")
        print(f"\n📊 图表解读说明：")
        print(f"   • 纵轴显示频率：每个区间的数量占总数的比值（小数形式）")
        print(f"   • 所有柱子的高度加起来等于1")
        print(f"   • 拟合曲线已调整尺度，可以直接对比")
    else:
        print("❌ 可视化分析失败")
#%% md
## 2. 数量统计 
#%%
import os
import numpy as np
import matplotlib.pyplot as plt
import pickle

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def load_statistics_data():
    """加载统计数据"""
    results_file = "result0/number_statistics_results.pkl"
    with open(results_file, 'rb') as f:
        return pickle.load(f)


def create_hourly_bar_chart(results_data):
    """创建时间分布柱状图"""
    # 提取数据
    cross_hourly = results_data['cross_related_encounters']['hourly_stats']
    non_cross_hourly = results_data['non_cross_encounters']['hourly_stats']

    hours = range(24)
    cross_counts = [cross_hourly.get(h, {}).get('encounter_count', 0) for h in hours]
    non_cross_counts = [non_cross_hourly.get(h, {}).get('encounter_count', 0) for h in hours]

    # 创建柱状图
    fig, ax = plt.subplots(figsize=(12, 6))

    # 设置背景为白色，移除填充
    ax.set_facecolor('white')
    fig.patch.set_facecolor('white')

    width = 0.35
    x = np.arange(len(hours))

    ax.bar(x - width / 2, cross_counts, width, color='#FF6B6B', alpha=0.8, label='交叉会遇')
    ax.bar(x + width / 2, non_cross_counts, width, color='#4ECDC4', alpha=0.8, label='追越会遇')

    ax.set_xticks(x)
    # 修改横坐标标签格式为时间段，并旋转45度
    ax.set_xticklabels([f'{h:02d}-{(h + 1) % 24:02d}' for h in hours], rotation=45)

    # 设置网格：灰色虚线
    ax.grid(True, linestyle='--', color='gray', alpha=0.7, linewidth=0.8)

    # 设置坐标轴刻度线：黑色实线
    ax.tick_params(axis='both', which='major', color='black',
                   length=6, width=1, direction='in')
    ax.tick_params(axis='both', which='minor', color='black',
                   length=3, width=0.5, direction='in')

    # 设置坐标轴字体为 Times New Roman
    for tick in ax.get_xticklabels():
        tick.set_fontname('Times New Roman')
    for tick in ax.get_yticklabels():
        tick.set_fontname('Times New Roman')

    # 设置所有坐标轴边框：黑色实线，保留四条边
    for spine in ax.spines.values():
        spine.set_color('black')
        spine.set_linewidth(1)
        spine.set_visible(True)

    # 添加图例：白色背景方框
    ax.legend(facecolor='white', edgecolor='black', frameon=True,
              framealpha=1.0, borderpad=0.5)

    plt.tight_layout()
    return fig


def create_hourly_line_chart(results_data):
    """创建时间分布折线图"""
    # 提取数据
    cross_hourly = results_data['cross_related_encounters']['hourly_stats']
    non_cross_hourly = results_data['non_cross_encounters']['hourly_stats']
    overall_hourly = results_data['overall_hourly_stats']

    hours = range(24)
    cross_counts = [cross_hourly.get(h, {}).get('encounter_count', 0) for h in hours]
    non_cross_counts = [non_cross_hourly.get(h, {}).get('encounter_count', 0) for h in hours]
    ship_counts = [overall_hourly.get(h, {}).get('ship_count', 0) for h in hours]

    # 创建折线图
    fig, ax = plt.subplots(figsize=(12, 6))

    # 设置背景为白色，移除填充
    ax.set_facecolor('white')
    fig.patch.set_facecolor('white')

    ax.plot(hours, cross_counts, color='#FF6B6B', linewidth=4, marker='o', markersize=4, label='交叉会遇数量')
    ax.plot(hours, non_cross_counts, color='#4ECDC4', linewidth=4, marker='s', markersize=4, label='追越会遇数量')
    ax.plot(hours, ship_counts, color='#2C3E50', linewidth=4, marker='^', markersize=4, label='船舶数量')

    # 显示所有小时的刻度，并旋转标签
    ax.set_xticks(hours)
    ax.set_xticklabels([f'{h:02d}-{(h + 1) % 24:02d}' for h in hours], rotation=45)

    # 设置网格：灰色虚线
    ax.grid(True, linestyle='--', color='gray', alpha=0.7, linewidth=0.8)

    # 设置坐标轴刻度线：黑色实线
    ax.tick_params(axis='both', which='major', color='black',
                   length=6, width=1, direction='in')
    ax.tick_params(axis='both', which='minor', color='black',
                   length=3, width=0.5, direction='in')

    # 设置坐标轴字体为 Times New Roman
    for tick in ax.get_xticklabels():
        tick.set_fontname('Times New Roman')
    for tick in ax.get_yticklabels():
        tick.set_fontname('Times New Roman')

    # 设置所有坐标轴边框：黑色实线，保留四条边
    for spine in ax.spines.values():
        spine.set_color('black')
        spine.set_linewidth(1)
        spine.set_visible(True)

    # 添加图例：白色背景方框
    ax.legend(facecolor='white', edgecolor='black', frameon=True,
              framealpha=1.0, borderpad=0.5)

    plt.tight_layout()
    return fig


def main():
    """主函数"""
    # 检查文件
    results_file = "result0/number_statistics_results.pkl"
    if not os.path.exists(results_file):
        print("错误: 未找到统计结果文件")
        return

    # 加载数据
    results_data = load_statistics_data()

    # 生成柱状图
    fig1 = create_hourly_bar_chart(results_data)
    fig1.savefig('vis0/hourly_encounters_bar.png', dpi=300, bbox_inches='tight')
    plt.close(fig1)
    print("柱状图已保存: vis0/hourly_encounters_bar.png")

    # 生成折线图
    fig2 = create_hourly_line_chart(results_data)
    fig2.savefig('vis0/hourly_encounters_line.png', dpi=300, bbox_inches='tight')
    plt.close(fig2)
    print("折线图已保存: vis0/hourly_encounters_line.png")


if __name__ == '__main__':
    main()
#%% md
## 3. 网格统计 
#%%
import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from PIL import Image

plt.rcParams['font.sans-serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


class GridHeatmapVisualization:
    """网格热力图可视化器"""

    def __init__(self, output_dir="vis0", intensity_multiplier=3.0, vmin=0, vmax=100, colormap='jet'):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
            intensity_multiplier: 强度倍数，用于调节颜色深浅
            vmin: 颜色映射最小值（人工设置的统一标准）
            vmax: 颜色映射最大值（人工设置的统一标准）
            colormap: 颜色映射名称
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.intensity_multiplier = intensity_multiplier
        self.vmin = vmin
        self.vmax = vmax

        # 颜色配置 - 精选的颜色映射
        self.available_colormaps = {
            'jet': 'jet',  # 蓝→青→绿→黄→红 (经典彩虹色谱)
            'turbo': 'turbo',  # 紫→蓝→绿→黄→橙→红 (改进版jet)
            'spectral': 'spectral',  # 红→橙→黄→绿→蓝→紫 (光谱色)
            'viridis_r': 'viridis_r'  # 黄→绿→蓝→紫 (反向viridis)
        }

        # 验证颜色映射是否可用
        if colormap in self.available_colormaps:
            current_colormap = colormap
        else:
            print(f"⚠️  警告: 颜色映射 '{colormap}' 不可用，使用默认的 'jet'")
            current_colormap = 'jet'

        self.colors = {
            'crossing': current_colormap,
            'overtaking': current_colormap,
            'combined': current_colormap
        }

    def load_grid_results(self, results_file=None):
        """
        加载网格统计结果
        
        Args:
            results_file: 结果文件路径，默认为grid_statistics_results.pkl
            
        Returns:
            dict: 网格统计结果数据
        """
        if results_file is None:
            results_file = Path("result0") / "grid_statistics_results.pkl"

        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"成功加载网格统计结果: {results_file}")
            return results
        except FileNotFoundError:
            print(f"错误: 未找到结果文件 {results_file}")
            print("请先运行 03.grid_statistics.py 生成网格统计结果")
            return None
        except Exception as e:
            print(f"加载结果文件时出错: {e}")
            return None

    def create_heatmap(self, grid_counts, colormap, vmin=None, vmax=None,
                       grid_settings=None, figsize=(12, 10), background_map_path="data/map0.png",
                       intensity_multiplier=3.0):
        """
        创建单个热力图

        Args:
            grid_counts: 网格计数数组
            colormap: 颜色映射
            vmin, vmax: 颜色范围
            grid_settings: 网格设置信息
            figsize: 图片大小
            background_map_path: 背景地图路径
            intensity_multiplier: 强度倍数，用于加深颜色显示

        Returns:
            matplotlib.figure.Figure: 生成的图表
        """
        fig, ax = plt.subplots(figsize=figsize)

        # 设置背景为白色
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        # 加载并显示背景地图
        try:
            background_img = Image.open(background_map_path)
            # 显示背景地图
            ax.imshow(background_img, extent=[0, grid_counts.shape[1], 0, grid_counts.shape[0]],
                      aspect='auto', alpha=0.6)
        except Exception as e:
            print(f"无法加载背景地图 {background_map_path}: {e}")

        # 等比例提高数量以加深颜色显示
        enhanced_counts = grid_counts * intensity_multiplier

        # 创建热力图 - 只显示有数据的区域
        masked_counts = np.ma.masked_where(enhanced_counts == 0, enhanced_counts)
        im = ax.imshow(masked_counts, cmap=colormap, aspect='auto',
                       origin='lower', vmin=vmin, vmax=vmax, alpha=0.8)

        # ====== 设置经纬度刻度和坐标轴 ======
        if grid_settings is not None:
            # 从 grid_settings 中获取经纬度边界
            bounds = grid_settings.get('bounds', {})
            lon_min = bounds.get('min_lon', 121.05)
            lon_max = bounds.get('max_lon', 121.35)
            lat_min = bounds.get('min_lat', 31.516)
            lat_max = bounds.get('max_lat', 31.784)
            
            nrows, ncols = grid_counts.shape

            # 刻度数量可根据实际调整
            num_xticks = 6
            num_yticks = 6

            # 计算刻度位置和标签
            x_ticks = np.linspace(0, ncols - 1, num=num_xticks)
            y_ticks = np.linspace(0, nrows - 1, num=num_yticks)
            x_labels = np.linspace(lon_min, lon_max, num=num_xticks)
            y_labels = np.linspace(lat_min, lat_max, num=num_yticks)

            # 设置刻度
            ax.set_xticks(x_ticks)
            ax.set_xticklabels([f"{x:.3f}" for x in x_labels], fontsize=10)
            ax.set_yticks(y_ticks)
            ax.set_yticklabels([f"{y:.3f}" for y in y_labels], fontsize=10)
            
            # 设置坐标轴标签
            # ax.set_xlabel("经度", fontsize=12, fontweight='bold')
            # ax.set_ylabel("纬度", fontsize=12, fontweight='bold')

            # ====== 设置四周边框和刻度向内 ======
            # 显示所有坐标轴边框
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color('black')
                spine.set_linewidth(1.2)

            # 设置刻度向内，四周都有刻度，底部和左侧显示标签
            ax.tick_params(axis='both', which='major', 
                          direction='in',  # 刻度向内
                          length=6,        # 刻度长度
                          width=1.2,       # 刻度宽度
                          color='black',   # 刻度颜色
                          top=True,        # 顶部显示刻度
                          right=True,      # 右侧显示刻度
                          bottom=True,     # 底部显示刻度
                          left=True,       # 左侧显示刻度
                          labeltop=False,  # 顶部不显示标签
                          labelright=False, # 右侧不显示标签
                          labelbottom=True, # 底部显示标签
                          labelleft=True,   # 左侧显示标签
                          labelsize=10)     # 标签字体大小

            # 添加灰色虚线网格
            ax.grid(True, linestyle='--', color='gray', alpha=0.5, linewidth=0.8)
            ax.set_axisbelow(True)  # 确保网格在热力图下方

        else:
            # 如果没有 grid_settings，仍然设置基本的坐标轴格式
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color('black')
                spine.set_linewidth(1.2)
            
            ax.tick_params(axis='both', which='major',
                          direction='in', length=6, width=1.2, color='black',
                          top=True, right=True, bottom=True, left=True)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02, aspect=30)
        # cbar.set_label('会遇数量', fontsize=12, fontweight='bold')
        # 
        # # 设置颜色条刻度
        # cbar.ax.tick_params(labelsize=10, direction='in', length=4, width=1)
        
        # 设置颜色条边框
        cbar.outline.set_linewidth(1.2)

        plt.tight_layout()
        return fig

    def analyze_and_visualize_grids(self, grid_results):
        """
        分析并可视化网格数据
        
        Args:
            grid_results: 网格统计结果
            
        Returns:
            dict: 生成的图表信息
        """
        if not grid_results:
            return None

        print("=== 开始网格热力图可视化分析 ===")

        # 提取数据
        grid_settings = grid_results.get('grid_settings', {})
        crossing_counts = grid_results['crossing_encounters']['grid_counts']
        overtaking_counts = grid_results['overtaking_encounters']['grid_counts']

        generated_plots = {}

        # 使用人工设置的统一标准颜色范围
        vmin = self.vmin
        vmax = self.vmax

        # 计算实际数据范围用于参考
        enhanced_crossing = crossing_counts * self.intensity_multiplier
        enhanced_overtaking = overtaking_counts * self.intensity_multiplier

        # 原始数据范围
        original_min = min(np.min(crossing_counts[crossing_counts > 0]) if np.any(crossing_counts > 0) else 0,
                           np.min(overtaking_counts[overtaking_counts > 0]) if np.any(overtaking_counts > 0) else 0)
        original_max = max(np.max(crossing_counts), np.max(overtaking_counts))

        # 放大后数据范围
        enhanced_min = min(np.min(enhanced_crossing[enhanced_crossing > 0]) if np.any(enhanced_crossing > 0) else 0,
                           np.min(enhanced_overtaking[enhanced_overtaking > 0]) if np.any(
                               enhanced_overtaking > 0) else 0)
        enhanced_max = max(np.max(enhanced_crossing), np.max(enhanced_overtaking))

        print(f"人工设置的统一颜色范围: [{vmin}, {vmax}]")
        print(f"原始数据范围: [{original_min:.1f}, {original_max:.1f}]")
        print(f"放大后数据范围: [{enhanced_min:.1f}, {enhanced_max:.1f}] (供参考)")
        print(f"当前使用颜色映射: {self.colors['crossing']}")

        if enhanced_max > vmax:
            print(f"⚠️  警告: 放大后最大值({enhanced_max:.1f})超过设置范围({vmax})，超出部分将显示为最深色")
        if enhanced_min < vmin:
            print(f"⚠️  警告: 放大后最小值({enhanced_min:.1f})低于设置范围({vmin})，低于部分将显示为最浅色")

        # 1. 交叉会遇热力图
        print("\n1. 生成交叉会遇热力图...")
        print(f"   使用强度倍数: {self.intensity_multiplier}")
        fig1 = self.create_heatmap(
            crossing_counts,
            self.colors['crossing'],
            vmin=vmin, vmax=vmax,
            grid_settings=grid_settings,
            intensity_multiplier=self.intensity_multiplier
        )
        if fig1:
            output_file1 = self.output_dir / 'crossing_encounters_heatmap.png'
            fig1.savefig(output_file1, dpi=300, bbox_inches='tight')
            print(f"   图表已保存: {output_file1}")
            generated_plots['crossing_heatmap'] = output_file1
            plt.close(fig1)

        # 2. 追越会遇热力图
        print("\n2. 生成追越会遇热力图...")
        print(f"   使用强度倍数: {self.intensity_multiplier}")
        fig2 = self.create_heatmap(
            overtaking_counts,
            self.colors['overtaking'],
            vmin=vmin, vmax=vmax,
            grid_settings=grid_settings,
            intensity_multiplier=self.intensity_multiplier
        )
        if fig2:
            output_file2 = self.output_dir / 'overtaking_encounters_heatmap.png'
            fig2.savefig(output_file2, dpi=300, bbox_inches='tight')
            print(f"   图表已保存: {output_file2}")
            generated_plots['overtaking_heatmap'] = output_file2
            plt.close(fig2)

        return generated_plots

    def generate_summary_report(self, grid_results, generated_plots):
        """
        生成可视化摘要报告
        
        Args:
            grid_results: 网格统计结果
            generated_plots: 生成的图表信息
        """
        print("\n" + "=" * 60)
        print("网格热力图可视化分析摘要")
        print("=" * 60)

        summary = grid_results['summary']

        print(f"轨迹点分布:")
        print(f"  交叉会遇轨迹点: {summary['total_crossing_points']}")
        print(f"  追越会遇轨迹点: {summary['total_overtaking_points']}")
        print(f"  总轨迹点数: {summary['total_points']}")

        print(f"\n生成的热力图: {len(generated_plots)} 张")
        for key, path in generated_plots.items():
            print(f"  - {key}: {path}")


def main_heatmap_visualization(intensity_multiplier=5.0, vmin=0, vmax=100, colormap='jet'):
    """
    主要热力图可视化分析函数
    
    Args:
        intensity_multiplier: 强度倍数，建议值：1.0-10.0，数值越大颜色越深
        vmin: 颜色映射最小值（统一标准）
        vmax: 颜色映射最大值（统一标准）
        colormap: 颜色映射名称
    
    Returns:
        dict: 可视化结果摘要
    """
    print("=== 开始网格热力图可视化分析 ===")
    print(f"颜色强度倍数设置: {intensity_multiplier}")
    print(f"统一颜色范围标准: [{vmin}, {vmax}]")
    print(f"选择的颜色映射: {colormap}")

    # 初始化可视化器
    visualizer = GridHeatmapVisualization(
        intensity_multiplier=intensity_multiplier,
        vmin=vmin,
        vmax=vmax,
        colormap=colormap
    )

    # 加载网格统计数据
    grid_results = visualizer.load_grid_results()
    if not grid_results:
        return None

    # 生成热力图
    generated_plots = visualizer.analyze_and_visualize_grids(grid_results)

    # 生成摘要报告
    visualizer.generate_summary_report(grid_results, generated_plots)

    print(f"\n=== 网格热力图可视化完成 ===")
    print(f"图表保存位置: vis0/")
    print(f"  - 热力图数量: {len(generated_plots)}")

    return {
        'heatmap_plots': generated_plots,
        'total_plots': len(generated_plots),
        'intensity_multiplier': intensity_multiplier
    }


if __name__ == '__main__':
    # 检查依赖文件
    results_file = "result0/grid_statistics_results.pkl"
    if not os.path.exists(results_file):
        print("错误: 未找到网格统计结果文件")
        print(f"请先运行 03.grid_statistics.py 生成 {results_file}")
        exit(1)

    # 执行热力图可视化分析
    # 您可以调整以下参数来控制可视化效果:
    # - intensity_multiplier: 控制颜色深浅 (建议值：1.0-10.0)
    # - vmin, vmax: 统一的颜色映射范围标准
    # - colormap: 颜色映射选择
    visualization_results = main_heatmap_visualization(
        intensity_multiplier=5.0,  # 颜色强度倍数
        vmin=0,  # 颜色映射最小值（统一标准）
        vmax=100,  # 颜色映射最大值（统一标准）
        colormap='turbo'  # 颜色映射 (可选: jet, turbo, spectral, viridis_r)
    )

    if visualization_results:
        print(f"\n✅ 网格热力图可视化完成！")
        print(f"   生成热力图总数: {visualization_results['total_plots']}")
        print(f"   查看 vis0/ 目录中的PNG文件") 
#%% md
## 4. 空间自相关
#%%
import os
import pickle
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
# === 新增官方库导入 ===
from esda.moran import Moran_Local
from libpysal.weights import lat2W

plt.rcParams['font.sans-serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


class WaterMaskedLocalMoranAnalysis:
    """基于水域掩码的局部莫兰指数分析器"""

    def __init__(self, output_dir="vis0", intensity_multiplier=3.0, vmin=0, vmax=100, colormap='jet'):
        """
        初始化分析器

        Args:
            output_dir: 输出目录
            intensity_multiplier: 强度倍数，用于调节颜色深浅
            vmin: 颜色映射最小值（人工设置的统一标准）
            vmax: 颜色映射最大值（人工设置的统一标准）
            colormap: 颜色映射名称
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.intensity_multiplier = intensity_multiplier
        self.vmin = vmin
        self.vmax = vmax

        # 颜色配置 - 适合局部莫兰指数的颜色映射（正负值）
        self.available_colormaps = {
            'RdBu_r': 'RdBu_r',  # 红→白→蓝 (红色正值，蓝色负值) - 经典推荐
            'BrBG': 'BrBG',  # 棕色→白→绕 (棕色负值，绿色正值) - 当前使用
            'PiYG': 'PiYG',  # 粉色→白→绿色 (粉色负值，绿色正值) - 柔和对比
            'PRGn': 'PRGn',  # 紫色→白→绿色 (紫色负值，绿色正值) - 强对比
            'PuOr': 'PuOr',  # 紫色→白→橙色 (紫色负值，橙色正值) - 艺术感
            'RdYlBu': 'RdYlBu',  # 红→黄→蓝 (红色正值，蓝色负值) - 三色渐变
            'coolwarm': 'coolwarm',  # 蓝→白→红 (蓝色负值，红色正值) - 温度感
            'seismic': 'seismic'  # 蓝→白→红 (地震图谱) - 科学可视化
        }

        # 验证颜色映射是否可用
        if colormap in self.available_colormaps:
            current_colormap = colormap
        else:
            print(f"⚠️  警告: 颜色映射 '{colormap}' 不可用，使用默认的 'RdBu_r'")
            current_colormap = 'RdBu_r'

        self.colors = {
            'crossing': current_colormap,
            'overtaking': current_colormap,
        }

    def load_grid_results(self, results_file=None):
        """
        加载网格统计结果

        Args:
            results_file: 结果文件路径，默认为grid_statistics_results.pkl

        Returns:
            dict: 网格统计结果数据
        """
        if results_file is None:
            results_file = Path("result0") / "grid_statistics_results.pkl"

        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"成功加载网格统计结果: {results_file}")
            return results
        except FileNotFoundError:
            print(f"错误: 未找到结果文件 {results_file}")
            print("请先运行 03.grid_statistics.py 生成网格统计结果")
            return None
        except Exception as e:
            print(f"加载结果文件时出错: {e}")
            return None

    def _load_water_boundary_data(self):
        """
        加载水域边界数据

        Returns:
            dict: 包含水域边界信息的字典
        """
        geo_info_file = Path("data") / "geo_info.pkl"

        try:
            with open(geo_info_file, 'rb') as f:
                geo_info = pickle.load(f)

            print(f"✅ 成功加载地理信息: {geo_info_file}")
            print(f"   可用边界数据: {list(geo_info.keys())}")

            return geo_info

        except FileNotFoundError:
            print(f"❌ 错误: 未找到地理信息文件 {geo_info_file}")
            return None
        except Exception as e:
            print(f"❌ 加载地理信息时出错: {e}")
            return None

    def _grid_to_latlon(self, row_idx, col_idx, bounds, grid_shape):
        """
        将网格索引转换为经纬度坐标（网格中心点）

        Args:
            row_idx: 行索引
            col_idx: 列索引
            bounds: 边界信息
            grid_shape: 网格形状 (rows, cols)

        Returns:
            tuple: (lat, lon)
        """
        rows, cols = grid_shape

        # 从bounds中获取地理边界
        min_lat = bounds.get('min_lat', 31.516667)
        max_lat = bounds.get('max_lat', 31.783333)
        min_lon = bounds.get('min_lon', 121.05)
        max_lon = bounds.get('max_lon', 121.35)

        # 计算每个网格的度数大小
        lat_step = (max_lat - min_lat) / rows
        lon_step = (max_lon - min_lon) / cols

        # 计算网格中心点坐标
        lat = min_lat + (row_idx + 0.5) * lat_step
        lon = min_lon + (col_idx + 0.5) * lon_step

        return lat, lon

    def _point_in_water_area(self, lat, lon, geo_info):
        """
        判断点是否在水域内（航道+锚地）

        Args:
            lat: 纬度
            lon: 经度
            geo_info: 地理信息数据

        Returns:
            bool: True表示在水域内
        """
        try:
            # 导入点在多边形内的判断函数
            from methods.Is_in_polygan import is_point_in_polygon

            # 检查是否在主航道边界内
            if 'channel_boundary' in geo_info:
                if is_point_in_polygon(lat, lon, geo_info['channel_boundary']):
                    return True

            # 检查是否在锚地内
            if 'anchorage' in geo_info and geo_info['anchorage']:
                if is_point_in_polygon(lat, lon, geo_info['anchorage']):
                    return True

            # 如果没有明确的边界数据，可以基于航道侧边界进行判断
            if 'channel_side1' in geo_info and 'channel_side2' in geo_info:
                # 简化处理：如果在任一侧边界多边形内，认为在水域内
                side1_boundary = geo_info['channel_side1']
                side2_boundary = geo_info['channel_side2']

                if (is_point_in_polygon(lat, lon, side1_boundary) or
                        is_point_in_polygon(lat, lon, side2_boundary)):
                    return True

            return False

        except Exception as e:
            print(f"⚠️  判断点({lat:.6f}, {lon:.6f})是否在水域内时出错: {e}")
            return False

    def _create_water_mask(self, grid_results):
        """
        创建水域掩码矩阵

        Args:
            grid_results: 网格统计结果

        Returns:
            np.ndarray: 布尔类型的水域掩码矩阵
        """
        print("=== 创建水域掩码 ===")

        # 加载水域边界数据
        geo_info = self._load_water_boundary_data()
        if geo_info is None:
            print("❌ 无法加载水域边界数据，将使用全部网格")
            grid_shape = grid_results['crossing_encounters']['grid_counts'].shape
            return np.ones(grid_shape, dtype=bool)

        # 获取网格信息
        grid_settings = grid_results.get('grid_settings', {})
        grid_shape = grid_results['crossing_encounters']['grid_counts'].shape
        bounds = grid_settings.get('bounds', {})

        print(f"   网格形状: {grid_shape}")
        print(f"   网格边界: {bounds}")

        # 创建掩码矩阵
        water_mask = np.zeros(grid_shape, dtype=bool)
        rows, cols = grid_shape

        print("   正在判断每个网格是否在水域内...")

        water_count = 0
        for i in range(rows):
            for j in range(cols):
                # 计算网格中心点经纬度
                lat, lon = self._grid_to_latlon(i, j, bounds, grid_shape)

                # 判断是否在水域内
                if self._point_in_water_area(lat, lon, geo_info):
                    water_mask[i, j] = True
                    water_count += 1

        total_grids = rows * cols
        water_ratio = water_count / total_grids * 100

        print(f"   ✅ 水域掩码创建完成")
        print(f"   水域网格数量: {water_count} / {total_grids}")
        print(f"   水域覆盖率: {water_ratio:.1f}%")

        if water_count == 0:
            print("   ⚠️  警告: 未检测到水域网格，可能边界数据有问题")
            # 新增备选方案
            print("   将使用简化方法：基于数据值创建掩码")
            crossing_counts = grid_results['crossing_encounters']['grid_counts']
            overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
            water_mask = (crossing_counts > 0) | (overtaking_counts > 0)
            water_count = np.sum(water_mask)
            print(f"   备选方案创建的水域网格数量: {water_count}")

        return water_mask

    def _calculate_water_masked_moran(self, data_matrix, water_mask, data_type=""):
        """
        计算基于水域掩码的局部莫兰指数

        Args:
            data_matrix: 网格数据矩阵
            water_mask: 水域掩码
            data_type: 数据类型名称（用于日志）

        Returns:
            np.array: 局部莫兰指数矩阵
        """
        rows, cols = data_matrix.shape

        # 只考虑水域网格的数据
        water_data = data_matrix[water_mask]
        n_water = len(water_data)

        print(f"   {data_type}水域网格数量: {n_water}")

        if n_water == 0:
            print(f"   ⚠️  {data_type}无水域网格，返回零矩阵")
            return np.zeros_like(data_matrix, dtype=float)

        # 计算水域网格的均值
        x_bar = np.mean(water_data)
        print(f"   {data_type}水域网格均值: {x_bar:.4f}")

        # 初始化结果矩阵
        local_moran = np.zeros_like(data_matrix, dtype=float)

        # 计算分母（只包含水域网格）
        water_centered = water_data - x_bar
        denominator = np.sum(water_centered ** 2)

        if denominator == 0:
            print(f"   ⚠️  {data_type}水域数据方差为0，返回零矩阵")
            return local_moran

        print(f"   正在计算{data_type}水域网格的局部莫兰指数...")

        # 为每个水域网格计算局部莫兰指数
        calculated_count = 0
        for i in range(rows):
            for j in range(cols):
                if not water_mask[i, j]:  # 跳过陆地网格
                    continue

                xi_centered = data_matrix[i, j] - x_bar

                # 找到水域邻居（只考虑rook邻接：上下左右）
                water_neighbors = []
                neighbor_positions = [(i - 1, j), (i + 1, j), (i, j - 1), (i, j + 1)]

                for ni, nj in neighbor_positions:
                    if (0 <= ni < rows and 0 <= nj < cols and
                            water_mask[ni, nj]):  # 只考虑水域邻居
                        water_neighbors.append(data_matrix[ni, nj])

                if len(water_neighbors) > 0:
                    # 计算空间滞后（邻居的中心化值的平均）
                    spatial_lag = np.mean([val - x_bar for val in water_neighbors])

                    # 计算局部莫兰指数
                    # Ii = ((n-1) * (xi - x̄) * 空间滞后) / Σ(xj - x̄)²
                    local_moran[i, j] = ((n_water - 1) * xi_centered * spatial_lag) / denominator
                    calculated_count += 1
                else:
                    # 如果没有水域邻居，设为0
                    local_moran[i, j] = 0.0

        print(f"   {data_type}计算完成，有效网格数: {calculated_count}")
        
        # 安全地计算统计信息
        if calculated_count > 0:
            non_zero_values = local_moran[local_moran != 0]
            if len(non_zero_values) > 0:
                print(f"   {data_type}Ii值范围: [{np.min(non_zero_values):.4f}, {np.max(non_zero_values):.4f}]")
            else:
                print(f"   {data_type}所有Ii值为0")
        else:
            print(f"   {data_type}无有效Ii值")
            
        print(f"   {data_type}正值网格数: {np.sum(local_moran > 0)}")
        print(f"   {data_type}负值网格数: {np.sum(local_moran < 0)}")

        return local_moran

    def calculate_water_masked_local_moran_i(self, grid_results):
        """
        基于水域掩码计算局部莫兰指数

        Args:
            grid_results: 网格统计结果

        Returns:
            dict: 包含局部莫兰指数结果的字典
        """
        print("=== 开始基于水域掩码的局部莫兰指数计算 ===")

        # 1. 创建水域掩码
        water_mask = self._create_water_mask(grid_results)

        # 2. 获取网格数据
        crossing_counts = grid_results['crossing_encounters']['grid_counts']
        overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
        grid_settings = grid_results.get('grid_settings', {})
        grid_shape = crossing_counts.shape

        print(f"\n网格形状: {grid_shape}")
        print(f"总网格数: {np.prod(grid_shape)}")
        print(f"水域网格数: {np.sum(water_mask)}")

        # 3. 计算局部莫兰指数（只针对水域网格）
        print("\n1. 计算交叉会遇水域局部莫兰指数...")
        ii_crossing = self._calculate_water_masked_moran(crossing_counts, water_mask, "交叉会遇")

        print("\n2. 计算追越会遇水域局部莫兰指数...")
        ii_overtaking = self._calculate_water_masked_moran(overtaking_counts, water_mask, "追越会遇")

        # 4. 计算统计信息
        statistics = self._calculate_water_statistics(ii_crossing, ii_overtaking, water_mask)

        print(f"\n=== 基于水域掩码的局部莫兰指数计算完成 ===")
        print(f"交叉会遇水域Ii值范围: [{np.min(ii_crossing):.4f}, {np.max(ii_crossing):.4f}]")
        print(f"追越会遇水域Ii值范围: [{np.min(ii_overtaking):.4f}, {np.max(ii_overtaking):.4f}]")

        return {
            'ii_crossing': ii_crossing,
            'ii_overtaking': ii_overtaking,
            'water_mask': water_mask,
            'grid_settings': grid_settings,
            'statistics': statistics
        }

    def _calculate_water_statistics(self, ii_crossing, ii_overtaking, water_mask):
        """
        计算水域网格的统计信息

        Args:
            ii_crossing: 交叉会遇局部莫兰指数矩阵
            ii_overtaking: 追越会遇局部莫兰指数矩阵
            water_mask: 水域掩码

        Returns:
            dict: 统计信息
        """
        # 只计算水域网格的统计
        crossing_water_ii = ii_crossing[water_mask]
        overtaking_water_ii = ii_overtaking[water_mask]

        return {
            'total_water_grids': np.sum(water_mask),
            'crossing_ii_mean_water': np.mean(crossing_water_ii) if len(crossing_water_ii) > 0 else 0,
            'overtaking_ii_mean_water': np.mean(overtaking_water_ii) if len(overtaking_water_ii) > 0 else 0,
            'crossing_positive_ii_water': np.sum(crossing_water_ii > 0),
            'overtaking_positive_ii_water': np.sum(overtaking_water_ii > 0),
            'crossing_negative_ii_water': np.sum(crossing_water_ii < 0),
            'overtaking_negative_ii_water': np.sum(overtaking_water_ii < 0),
            'crossing_zero_ii_water': np.sum(crossing_water_ii == 0),
            'overtaking_zero_ii_water': np.sum(overtaking_water_ii == 0)
        }

    def create_water_masked_local_moran_heatmap(self, ii_values, water_mask, colormap, vmin=None, vmax=None,
                                                grid_settings=None, figsize=(12, 10),
                                                background_map_path="data/map0.png",
                                                intensity_multiplier=1.0, title=""):
        """
        创建基于水域掩码的局部莫兰指数热力图

        Args:
            ii_values: 局部莫兰指数矩阵
            water_mask: 水域掩码
            colormap: 颜色映射
            vmin, vmax: 颜色范围
            grid_settings: 网格设置信息
            figsize: 图片大小
            background_map_path: 背景地图路径
            intensity_multiplier: 强度倍数，用于调节颜色显示
            title: 图表标题

        Returns:
            matplotlib.figure.Figure: 生成的图表
        """
        fig, ax = plt.subplots(figsize=figsize)

        # 设置背景为白色
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')

        # 加载并显示背景地图
        try:
            background_img = Image.open(background_map_path)
            ax.imshow(background_img, extent=[0, ii_values.shape[1], 0, ii_values.shape[0]],
                      aspect='auto', alpha=0.6)
        except Exception as e:
            print(f"无法加载背景地图 {background_map_path}: {e}")

        # 创建掩码数据：只显示水域网格的局部莫兰指数
        masked_ii = np.ma.masked_where(~water_mask, ii_values)  # 掩蔽非水域区域
        masked_ii = np.ma.masked_where(masked_ii == 0, masked_ii)  # 掩蔽零值

        # 创建热力图
        im = ax.imshow(masked_ii, cmap=colormap, aspect='auto',
                       origin='lower', vmin=vmin, vmax=vmax, alpha=0.8)

        # 设置经纬度刻度和坐标轴
        if grid_settings is not None:
            bounds = grid_settings.get('bounds', {})
            lon_min = bounds.get('min_lon', 121.05)
            lon_max = bounds.get('max_lon', 121.35)
            lat_min = bounds.get('min_lat', 31.516)
            lat_max = bounds.get('max_lat', 31.784)

            nrows, ncols = ii_values.shape

            # 刻度数量
            num_xticks = 6
            num_yticks = 6

            # 计算刻度位置和标签
            x_ticks = np.linspace(0, ncols - 1, num=num_xticks)
            y_ticks = np.linspace(0, nrows - 1, num=num_yticks)
            x_labels = np.linspace(lon_min, lon_max, num=num_xticks)
            y_labels = np.linspace(lat_min, lat_max, num=num_yticks)

            # 设置刻度
            ax.set_xticks(x_ticks)
            ax.set_xticklabels([f"{x:.3f}" for x in x_labels], fontsize=10)
            ax.set_yticks(y_ticks)
            ax.set_yticklabels([f"{y:.3f}" for y in y_labels], fontsize=10)

            # 设置四周边框和刻度向内
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color('black')
                spine.set_linewidth(1.2)

            ax.tick_params(axis='both', which='major',
                           direction='in', length=6, width=1.2, color='black',
                           top=True, right=True, bottom=True, left=True,
                           labeltop=False, labelright=False,
                           labelbottom=True, labelleft=True, labelsize=10)

            # 添加灰色虚线网格
            ax.grid(True, linestyle='--', color='gray', alpha=0.5, linewidth=0.8)
            ax.set_axisbelow(True)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02, aspect=30)
        # 取消颜色条标签
        # cbar.set_label('水域局部莫兰指数 Ii', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10, direction='in', length=4, width=1)
        cbar.outline.set_linewidth(1.2)

        # 取消图表标题
        # if title:
        #     ax.set_title(f"基于水域掩码的{title}", fontsize=14, fontweight='bold', pad=20)

        plt.tight_layout()
        return fig

    def analyze_and_visualize_water_masked_local_moran(self, ii_results):
        """
        分析并可视化基于水域掩码的局部莫兰指数数据

        Args:
            ii_results: 局部莫兰指数计算结果

        Returns:
            dict: 生成的图表路径
        """
        print("=== 开始基于水域掩码的局部莫兰指数热力图可视化 ===")

        ii_crossing = ii_results['ii_crossing']
        ii_overtaking = ii_results['ii_overtaking']
        water_mask = ii_results['water_mask']
        grid_settings = ii_results['grid_settings']
        statistics = ii_results['statistics']

        generated_plots = {}
        vmin = self.vmin
        vmax = self.vmax

        print(f"颜色范围设置: [{vmin}, {vmax}]")
        
        # 安全地获取水域数据范围
        crossing_water_values = ii_crossing[water_mask]
        overtaking_water_values = ii_overtaking[water_mask]
        
        if len(crossing_water_values) > 0:
            crossing_non_zero = crossing_water_values[crossing_water_values != 0]
            if len(crossing_non_zero) > 0:
                print(f"交叉会遇水域Ii值范围: [{np.min(crossing_non_zero):.4f}, {np.max(crossing_non_zero):.4f}]")
            else:
                print("交叉会遇水域所有Ii值为0")
        else:
            print("交叉会遇无水域数据")
            
        if len(overtaking_water_values) > 0:
            overtaking_non_zero = overtaking_water_values[overtaking_water_values != 0]
            if len(overtaking_non_zero) > 0:
                print(f"追越会遇水域Ii值范围: [{np.min(overtaking_non_zero):.4f}, {np.max(overtaking_non_zero):.4f}]")
            else:
                print("追越会遇水域所有Ii值为0")
        else:
            print("追越会遇无水域数据")

        # 1. 交叉会遇水域局部莫兰指数热力图
        print("\n1. 生成交叉会遇水域局部莫兰指数热力图...")
        fig1 = self.create_water_masked_local_moran_heatmap(
            ii_crossing, water_mask, self.colors['crossing'],
            vmin=vmin, vmax=vmax, grid_settings=grid_settings,
            intensity_multiplier=self.intensity_multiplier,
            title="交叉会遇局部莫兰指数 (Ii)"
        )
        if fig1:
            output_file1 = self.output_dir / 'crossing_water_masked_local_moran_ii.png'
            fig1.savefig(output_file1, dpi=300, bbox_inches='tight')
            print(f"   图表已保存: {output_file1}")
            generated_plots['crossing_ii'] = output_file1
            plt.close(fig1)

        # 2. 追越会遇水域局部莫兰指数热力图
        print("\n2. 生成追越会遇水域局部莫兰指数热力图...")
        fig2 = self.create_water_masked_local_moran_heatmap(
            ii_overtaking, water_mask, self.colors['overtaking'],
            vmin=vmin, vmax=vmax, grid_settings=grid_settings,
            intensity_multiplier=self.intensity_multiplier,
            title="追越会遇局部莫兰指数 (Ii)"
        )
        if fig2:
            output_file2 = self.output_dir / 'overtaking_water_masked_local_moran_ii.png'
            fig2.savefig(output_file2, dpi=300, bbox_inches='tight')
            print(f"   图表已保存: {output_file2}")
            generated_plots['overtaking_ii'] = output_file2
            plt.close(fig2)

        return generated_plots

    def generate_water_masked_summary_report(self, ii_results, generated_plots):
        """
        生成基于水域掩码的局部莫兰指数分析摘要报告

        Args:
            ii_results: 局部莫兰指数计算结果
            generated_plots: 生成的图表路径
        """
        print("\n" + "=" * 70)
        print("基于水域掩码的局部莫兰指数分析摘要")
        print("=" * 70)

        statistics = ii_results['statistics']
        grid_settings = ii_results['grid_settings']
        water_mask = ii_results['water_mask']

        total_grids = np.prod(water_mask.shape)
        water_grids = statistics['total_water_grids']

        print(f"网格设置:")
        print(f"  网格大小: {grid_settings.get('grid_size_meters', 'Unknown')}米")
        print(f"  网格形状: {grid_settings.get('grid_shape', 'Unknown')}")
        print(f"  总网格数: {total_grids}")
        print(f"  水域网格数: {water_grids}")
        print(f"  水域覆盖率: {water_grids / total_grids * 100:.1f}%")

        print(f"\n基于水域掩码的局部莫兰指数统计:")
        print(f"  交叉会遇水域平均Ii值: {statistics['crossing_ii_mean_water']:.4f}")
        print(f"  追越会遇水域平均Ii值: {statistics['overtaking_ii_mean_water']:.4f}")

        print(f"\n水域网格空间聚集分布:")
        print(f"  交叉会遇正值网格(聚集): {statistics['crossing_positive_ii_water']}")
        print(f"  交叉会遇负值网格(分散): {statistics['crossing_negative_ii_water']}")
        print(f"  交叉会遇零值网格(无关): {statistics['crossing_zero_ii_water']}")
        print(f"  追越会遇正值网格(聚集): {statistics['overtaking_positive_ii_water']}")
        print(f"  追越会遇负值网格(分散): {statistics['overtaking_negative_ii_water']}")
        print(f"  追越会遇零值网格(无关): {statistics['overtaking_zero_ii_water']}")

        print(f"\n生成的水域局部莫兰指数热力图: {len(generated_plots)} 张")
        for key, path in generated_plots.items():
            print(f"  - {key}: {path}")

        print(f"\n📊 基于水域掩码的局部莫兰指数解读:")
        print(f"   • 只考虑水域网格，排除陆地干扰")
        print(f"   • Ii > 0: 该水域网格与相邻水域网格呈相似模式（热点聚集）")
        print(f"   • Ii < 0: 该水域网格与相邻水域网格呈相异模式（异常分散）")
        print(f"   • |Ii| 越大表示水域空间自相关越强")
        print(f"   • 更准确地识别海上交通流的空间聚集模式")

        print(f"\n🔍 分析要点:")
        print(f"   • 空间权重只考虑水域邻接关系")
        print(f"   • 均值计算只基于水域网格数据")
        print(f"   • 消除了陆海边界的虚假空间效应")
        print(f"   • 结果更符合海上交通实际情况")


def main_water_masked_local_moran_analysis(intensity_multiplier=1.0, vmin=-1, vmax=1, colormap='RdBu_r'):
    """
    主要的基于水域掩码的局部莫兰指数分析函数

    Args:
        intensity_multiplier: 强度倍数
        vmin: 颜色映射最小值（局部莫兰指数可能为负）
        vmax: 颜色映射最大值
        colormap: 颜色映射名称（RdBu_r适合显示正负值）

    Returns:
        dict: 分析结果摘要
    """
    print("=== 开始基于水域掩码的局部莫兰指数分析 ===")
    print(f"只对水域网格计算Ii值: Ii = ((n_water-1)(xi-x̄_water)∑wij_water(xj-x̄_water))/∑_water(xj-x̄_water)²")
    print(f"颜色强度倍数: {intensity_multiplier}")
    print(f"颜色范围: [{vmin}, {vmax}]")
    print(f"颜色映射: {colormap}")

    # 初始化分析器
    analyzer = WaterMaskedLocalMoranAnalysis(
        intensity_multiplier=intensity_multiplier,
        vmin=vmin,
        vmax=vmax,
        colormap=colormap
    )

    # 加载网格数据
    grid_results = analyzer.load_grid_results()
    if not grid_results:
        return None

    # 计算基于水域掩码的局部莫兰指数
    ii_results = analyzer.calculate_water_masked_local_moran_i(grid_results)

    # 生成热力图
    generated_plots = analyzer.analyze_and_visualize_water_masked_local_moran(ii_results)

    # 生成摘要报告
    analyzer.generate_water_masked_summary_report(ii_results, generated_plots)

    print(f"\n=== 基于水域掩码的局部莫兰指数分析完成 ===")
    print(f"水域局部莫兰指数热力图保存位置: vis0/")

    return {
        'ii_results': ii_results,
        'generated_plots': generated_plots,
        'total_plots': len(generated_plots)
    }


if __name__ == '__main__':
    # 检查依赖文件
    grid_file = "result0/grid_statistics_results.pkl"
    geo_file = "data/geo_info.pkl"

    if not os.path.exists(grid_file):
        print("❌ 错误: 未找到网格统计结果文件")
        print(f"请先运行 03.grid_statistics.py 生成 {grid_file}")
        exit(1)

    if not os.path.exists(geo_file):
        print("❌ 错误: 未找到地理信息文件")
        print(f"请确保存在 {geo_file} 文件")
        exit(1)

    print("🎯 基于水域掩码的局部莫兰指数分析")
    print("✅ 只有水域网格参与计算")
    print("✅ 消除陆地网格干扰")
    print("✅ 数学上更加精确")

    # 执行基于水域掩码的局部莫兰指数分析
    analysis_results = main_water_masked_local_moran_analysis(
        intensity_multiplier=1.0,  # 颜色强度倍数
        vmin=-50,  # 局部莫兰指数最小值（可能为负）
        vmax=50,   # 局部莫兰指数最大值
        colormap='seismic'  # 改为更鲜艳的颜色映射：蓝→白→红（红色高值区很鲜艳）
    )

    if analysis_results:
        print(f"\n✅ 基于水域掩码的局部莫兰指数分析完成！")
        print(f"   生成水域局部莫兰指数热力图总数: {analysis_results['total_plots']}")
        print(f"   查看 vis0/ 目录中的PNG文件")
        print(f"\n🗺️ 生成的图片文件:")
        print(f"   - crossing_water_masked_local_moran_ii.png")
        print(f"   - overtaking_water_masked_local_moran_ii.png")
        print(f"\n📊 基于水域掩码的局部莫兰指数特点:")
        print(f"   • 只显示水域网格的空间聚集模式")
        print(f"   • 深红色区域: 正空间自相关（热点聚集）")
        print(f"   • 深蓝色区域: 负空间自相关（异常分散）")
        print(f"   • 白色区域: 无明显空间自相关或陆地区域")
        print(f"   • 消除了陆海边界的虚假效应")
        print(f"\n🔬 数学改进:")
        print(f"   • n: 改为水域网格总数（非全部网格）")
        print(f"   • x̄: 只计算水域网格的均值")
        print(f"   • wij: 空间权重只定义水域网格间的邻接")
        print(f"   • 分母: 只包含水域网格的离差平方和")
#%% md
## 5. 莫兰图
#%%
import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from scipy import stats
import pandas as pd
from scipy.sparse import lil_matrix, csr_matrix
from esda.moran import Moran
from libpysal.weights import lat2W

plt.rcParams['font.sans-serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False


class MoranAnalysis:
    """莫兰图空间自相关分析器"""

    def __init__(self, output_dir="vis0"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def load_grid_results(self, results_file=None):
        if results_file is None:
            results_file = Path("result0") / "grid_statistics_results.pkl"
        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"成功加载网格统计结果: {results_file}")
            return results
        except FileNotFoundError:
            print(f"错误: 未找到结果文件 {results_file}")
            print("请先运行 03.grid_statistics.py 生成网格统计结果")
            return None
        except Exception as e:
            print(f"加载结果文件时出错: {e}")
            return None

    def _load_water_boundary_data(self):
        """加载水域边界数据"""
        geo_info_file = Path("data") / "geo_info.pkl"
        try:
            with open(geo_info_file, 'rb') as f:
                geo_info = pickle.load(f)
            print(f"✅ 成功加载地理信息: {geo_info_file}")
            return geo_info
        except FileNotFoundError:
            print(f"❌ 错误: 未找到地理信息文件 {geo_info_file}")
            return None
        except Exception as e:
            print(f"❌ 加载地理信息时出错: {e}")
            return None

    def _grid_to_latlon(self, row_idx, col_idx, bounds, grid_shape):
        """将网格索引转换为经纬度坐标（网格中心点）"""
        rows, cols = grid_shape
        min_lat = bounds.get('min_lat', 31.516667)
        max_lat = bounds.get('max_lat', 31.783333)
        min_lon = bounds.get('min_lon', 121.05)
        max_lon = bounds.get('max_lon', 121.35)
        lat_step = (max_lat - min_lat) / rows
        lon_step = (max_lon - min_lon) / cols
        lat = min_lat + (row_idx + 0.5) * lat_step
        lon = min_lon + (col_idx + 0.5) * lon_step
        return lat, lon

    def _point_in_water_area(self, lat, lon, geo_info):
        """判断点是否在水域内"""
        try:
            from methods.Is_in_polygan import is_point_in_polygon
            if 'channel_boundary' in geo_info:
                if is_point_in_polygon(lat, lon, geo_info['channel_boundary']):
                    return True
            if 'anchorage' in geo_info and geo_info['anchorage']:
                if is_point_in_polygon(lat, lon, geo_info['anchorage']):
                    return True
            if 'channel_side1' in geo_info and 'channel_side2' in geo_info:
                side1_boundary = geo_info['channel_side1']
                side2_boundary = geo_info['channel_side2']
                if (is_point_in_polygon(lat, lon, side1_boundary) or 
                    is_point_in_polygon(lat, lon, side2_boundary)):
                    return True
            return False
        except Exception as e:
            print(f"⚠️  判断点({lat:.6f}, {lon:.6f})是否在水域内时出错: {e}")
            return False

    def _create_water_mask(self, grid_results, grid_shape):
        """创建水域掩码矩阵"""
        print("=== 创建水域掩码用于莫兰分析 ===")
        geo_info = self._load_water_boundary_data()
        if geo_info is None:
            print("❌ 无法加载水域边界数据，将使用全部网格")
            return np.ones(grid_shape, dtype=bool)

        grid_settings = grid_results.get('grid_settings', {})
        bounds = grid_settings.get('bounds', {})
        water_mask = np.zeros(grid_shape, dtype=bool)
        rows, cols = grid_shape

        water_count = 0
        for i in range(rows):
            for j in range(cols):
                lat, lon = self._grid_to_latlon(i, j, bounds, grid_shape)
                if self._point_in_water_area(lat, lon, geo_info):
                    water_mask[i, j] = True
                    water_count += 1

        if water_count == 0:
            print("   ⚠️  警告: 未检测到水域网格，使用数据驱动的掩码")
            crossing_counts = grid_results['crossing_encounters']['grid_counts']
            overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
            # 应用聚合如果需要
            if hasattr(self, '_temp_aggregation_factor'):
                crossing_counts = self.aggregate_grid_data(crossing_counts, self._temp_aggregation_factor)
                overtaking_counts = self.aggregate_grid_data(overtaking_counts, self._temp_aggregation_factor)
            water_mask = (crossing_counts > 0) | (overtaking_counts > 0)
            water_count = np.sum(water_mask)

        print(f"   水域网格数量: {water_count} / {np.prod(grid_shape)}")
        print(f"   水域覆盖率: {water_count/np.prod(grid_shape)*100:.1f}%")
        return water_mask

    def aggregate_grid_data(self, grid_data, aggregation_factor=4):
        """
        将细网格聚合为粗网格

        Args:
            grid_data: 原始网格数据
            aggregation_factor: 聚合因子（每个方向聚合的网格数）

        Returns:
            np.array: 聚合后的网格数据
        """
        rows, cols = grid_data.shape
        new_rows = rows // aggregation_factor
        new_cols = cols // aggregation_factor

        # 创建聚合后的网格
        aggregated = np.zeros((new_rows, new_cols))

        for i in range(new_rows):
            for j in range(new_cols):
                # 聚合区域的边界
                r_start = i * aggregation_factor
                r_end = (i + 1) * aggregation_factor
                c_start = j * aggregation_factor
                c_end = (j + 1) * aggregation_factor

                # 对区域内的值求和
                aggregated[i, j] = np.sum(grid_data[r_start:r_end, c_start:c_end])

        print(f"   网格聚合: {rows}x{cols} → {new_rows}x{new_cols} (因子={aggregation_factor})")
        return aggregated

    def create_spatial_weights_matrix(self, grid_shape, water_mask=None, weight_type='rook'):
        """
        使用PySAL创建空间权重矩阵（支持水域掩码）
        """
        rows, cols = grid_shape

        if water_mask is not None:
            # 创建基于水域掩码的权重矩阵
            print(f"   创建基于水域掩码的空间权重矩阵...")
            
            # 首先创建完整的网格权重矩阵
            if weight_type == 'rook':
                w_full = lat2W(rows, cols, rook=True)
            else:
                w_full = lat2W(rows, cols, rook=False)
            
            # 获取水域网格的线性索引
            water_indices = np.where(water_mask.flatten())[0]
            n_water = len(water_indices)
            
            if n_water == 0:
                print("   ⚠️  无水域网格，使用完整权重矩阵")
                return w_full
            
            # 创建水域网格间的邻接关系
            from libpysal.weights import W
            neighbors = {}
            weights = {}
            
            # 创建索引映射：从完整网格索引到水域子集索引
            full_to_water = {full_idx: water_idx for water_idx, full_idx in enumerate(water_indices)}
            
            for water_idx, full_idx in enumerate(water_indices):
                neighbors[water_idx] = []
                weights[water_idx] = []
                
                if full_idx in w_full.neighbors:
                    for neighbor_full_idx, weight in zip(w_full.neighbors[full_idx], w_full.weights[full_idx]):
                        # 只保留也在水域内的邻居
                        if neighbor_full_idx in full_to_water:
                            neighbor_water_idx = full_to_water[neighbor_full_idx]
                            neighbors[water_idx].append(neighbor_water_idx)
                            weights[water_idx].append(weight)
            
            # 创建水域权重矩阵
            w_water = W(neighbors, weights)
            w_water.transform = 'r'  # 行标准化
            
            print(f"   水域权重矩阵: {w_water.n} 个单元, {w_water.s0:.0f} 个连接")
            return w_water
        else:
            # 原始的完整网格权重矩阵
            if weight_type == 'rook':
                w = lat2W(rows, cols, rook=True)
            else:
                w = lat2W(rows, cols, rook=False)
            return w

    def calculate_morans_i(self, data_flat, W, water_mask_flat=None, use_valid_only=True, min_threshold=1):
        """
        使用PySAL官方库计算莫兰统计量（支持水域掩码）
        """
        try:
            # 如果有水域掩码，只使用水域数据
            if water_mask_flat is not None:
                water_data = data_flat[water_mask_flat]
                n_water = len(water_data)
                
                if n_water < 30:
                    print(f"警告: 水域数据点太少 ({n_water})，无法进行可靠的莫兰分析")
                    return None
                
                print(f"   使用水域数据: {n_water} 个网格")
                data_for_moran = water_data
                
                # 计算标准莫兰散点图的Z和WZ（基于水域数据）
                mean_val = np.mean(water_data)
                z_water = water_data - mean_val
                
                # 使用水域权重矩阵计算空间滞后
                wz_water = np.zeros_like(water_data)
                for i in range(len(water_data)):
                    if i in W.neighbors:
                        neighbors = W.neighbors[i]
                        weights = W.weights[i]
                        if len(neighbors) > 0:
                            wz_water[i] = np.sum([weights[j] * z_water[neighbors[j]] 
                                                for j in range(len(neighbors))])
                
                spatial_lag_z = wz_water
                z_values = z_water
                valid_mask = np.ones(len(water_data), dtype=bool)  # 所有水域数据都有效
                
            else:
                # 原始逻辑：使用全部数据
                if use_valid_only:
                    valid_mask = data_flat >= min_threshold
                    n_valid = np.sum(valid_mask)
                    if n_valid < 30:
                        print(f"警告: 有效数据点太少 ({n_valid})，无法进行可靠的莫兰分析")
                        return None
                
                data_for_moran = data_flat
                mean_val = np.mean(data_flat)
                z_values = data_flat - mean_val
                
                # 计算空间滞后
                wz = np.zeros_like(data_flat)
                for i in range(len(data_flat)):
                    if i in W.neighbors:
                        neighbors = W.neighbors[i]
                        weights = W.weights[i]
                        if len(neighbors) > 0:
                            wz[i] = np.sum([weights[j] * z_values[neighbors[j]]
                                          for j in range(len(neighbors))])
                spatial_lag_z = wz
                valid_mask = data_flat > 0 if use_valid_only else np.ones(len(data_flat), dtype=bool)

            # 对数变换减少极值影响
            data_transformed = np.log1p(data_for_moran)
            moran = Moran(data_transformed, W)

            print(f"   使用PySAL计算: I={moran.I:.4f}, EI={moran.EI:.4f}, VI={moran.VI_norm:.6f}")
            print(f"   Z-score={moran.z_norm:.4f}, p-value={moran.p_norm:.4f}")
            
            return {
                'morans_i': moran.I,
                'expected_i': moran.EI,
                'variance_i': moran.VI_norm,
                'z_score': moran.z_norm,
                'p_value': moran.p_norm,
                'z_values': z_values[valid_mask],
                'spatial_lag_z': spatial_lag_z[valid_mask],
                'valid_mask': valid_mask,
                'n_obs': len(data_for_moran),
                'weights_sum': W.s0
            }

        except Exception as e:
            print(f"PySAL计算莫兰统计量时出错: {e}")
            return None

    def create_moran_scatterplot(self, moran_result, title, figsize=(10, 8)):
        if moran_result is None:
            return None
        fig, ax = plt.subplots(figsize=figsize)
        ax.set_facecolor('white')
        fig.patch.set_facecolor('white')
        z_values = moran_result['z_values']
        spatial_lag_z = moran_result['spatial_lag_z']
        morans_i = moran_result['morans_i']
        n_obs = moran_result.get('n_obs', len(z_values))
        weights_sum = moran_result.get('weights_sum', 1.0)
        # ax.scatter(z_values, spatial_lag_z, alpha=0.3, s=20, c='black', edgecolors='black')
        ax.scatter(z_values, spatial_lag_z, alpha=0.8, s=20, facecolors='none', edgecolors='black', linewidth=1.2)
        if len(z_values) > 1:
            # 根据莫兰指数公式计算正确的斜率
            # slope = Σ(Zi * WZi) / Σ(Zi²)
            # 这个斜率与莫兰指数I的关系: I = (n * slope) / S0
            numerator = np.sum(z_values * spatial_lag_z)
            denominator = np.sum(z_values ** 2)

            if denominator > 0:
                moran_slope = numerator / denominator

                # 验证斜率与莫兰指数的关系
                # 理论关系: I = (n * slope) / S₀
                theoretical_moran = (n_obs * moran_slope) / weights_sum if weights_sum > 0 else 0
                slope_from_moran = (morans_i * weights_sum) / n_obs if n_obs > 0 else 0

                # 通过原点的直线 (莫兰散点图的理论基础)
                # 适度延伸线条，大约一个刻度的距离
                x_min, x_max = min(z_values), max(z_values)
                x_range = x_max - x_min

                # 计算一个刻度的大致距离（约为数据范围的10-15%）
                tick_distance = x_range * 0.12

                # 适度延伸，大约一个刻度的距离
                line_x_start = x_min - tick_distance
                line_x_end = x_max + tick_distance

                line_x = np.linspace(line_x_start, line_x_end, 100)
                line_y = moran_slope * line_x  # y = slope * x (过原点)
                ax.plot(line_x, line_y, 'r-', linewidth=2, alpha=0.8,
                        label=f'斜率={moran_slope:.4f}')

                print(f"   莫兰散点图斜率: {moran_slope:.4f}")
                print(f"   权重矩阵总和S₀: {weights_sum:.2f}")
                print(
                    f"   理论验证: I=(n×斜率)/S₀ = ({n_obs}×{moran_slope:.4f})/{weights_sum:.2f} = {theoretical_moran:.4f}")
                print(f"   实际莫兰指数I: {morans_i:.4f}")
                print(f"   误差: {abs(theoretical_moran - morans_i):.6f}")
            else:
                # 备选：使用线性回归
                slope, intercept, r_value, p_value, std_err = stats.linregress(z_values, spatial_lag_z)
                line_x = np.linspace(min(z_values), max(z_values), 100)
                line_y = slope * line_x + intercept
                ax.plot(line_x, line_y, 'r--', linewidth=2, alpha=0.8,
                        label=f'回归线斜率={slope:.4f}')
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.7, linewidth=1)
        ax.axvline(x=0, color='gray', linestyle='--', alpha=0.7, linewidth=1)
        ax.set_xlabel(r'$Z_i$', fontsize=12)
        ax.set_ylabel(r'$WZ_i$', fontsize=12)  # 标准莫兰公式中的WZ
        ax.grid(True, linestyle='--', color='gray', alpha=0.3, linewidth=0.5)
        ax.tick_params(axis='both', which='major', color='black', length=6, width=1, direction='in')
        for tick in ax.get_xticklabels():
            tick.set_fontname('Times New Roman')
        for tick in ax.get_yticklabels():
            tick.set_fontname('Times New Roman')
        for spine in ax.spines.values():
            spine.set_color('black')
            spine.set_linewidth(1)
            spine.set_visible(True)
        plt.tight_layout()
        return fig

    def analyze_spatial_autocorrelation(self, grid_results, use_aggregation=True, aggregation_factor=4):
        if not grid_results:
            return None
        print("=== 开始基于水域掩码的莫兰图空间自相关分析 ===")

        # 获取原始数据
        crossing_counts = grid_results['crossing_encounters']['grid_counts']
        overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
        original_shape = crossing_counts.shape
        print(f"原始网格形状: {original_shape}")

        # 数据聚合处理
        if use_aggregation and (original_shape[0] > 100 or original_shape[1] > 100):
            print(f"\n应用网格聚合减少数据规模...")
            self._temp_aggregation_factor = aggregation_factor  # 临时存储，供水域掩码创建使用
            crossing_counts = self.aggregate_grid_data(crossing_counts, aggregation_factor)
            overtaking_counts = self.aggregate_grid_data(overtaking_counts, aggregation_factor)
            grid_shape = crossing_counts.shape
        else:
            self._temp_aggregation_factor = 1
            grid_shape = original_shape

        # 创建水域掩码
        water_mask = self._create_water_mask(grid_results, grid_shape)
        water_mask_flat = water_mask.flatten()
        
        print(f"\n使用基于水域掩码的PySAL空间权重矩阵...")
        W = self.create_spatial_weights_matrix(grid_shape, water_mask, weight_type='rook')
        
        results = {}
        generated_plots = {}

        print("\n1. 分析交叉会遇的水域空间自相关...")
        crossing_flat = crossing_counts.flatten()
        print(f"   原始数据范围: [0, {np.max(crossing_flat)}], 非零值: {np.sum(crossing_flat > 0)}")
        crossing_moran = self.calculate_morans_i(crossing_flat, W, water_mask_flat, 
                                               use_valid_only=True, min_threshold=1)
        if crossing_moran:
            print(f"   水域Moran's I: {crossing_moran['morans_i']:.4f}")
            results['crossing'] = crossing_moran
            fig1 = self.create_moran_scatterplot(crossing_moran, "交叉会遇水域空间自相关分析")
            if fig1:
                output_file1 = self.output_dir / 'crossing_moran_scatterplot.png'
                fig1.savefig(output_file1, dpi=300, bbox_inches='tight')
                print(f"   散点图已保存: {output_file1}")
                generated_plots['crossing_moran'] = output_file1
                plt.close(fig1)

        print("\n2. 分析追越会遇的水域空间自相关...")
        overtaking_flat = overtaking_counts.flatten()
        print(f"   原始数据范围: [0, {np.max(overtaking_flat)}], 非零值: {np.sum(overtaking_flat > 0)}")
        overtaking_moran = self.calculate_morans_i(overtaking_flat, W, water_mask_flat,
                                                  use_valid_only=True, min_threshold=1)
        if overtaking_moran:
            print(f"   水域Moran's I: {overtaking_moran['morans_i']:.4f}")
            results['overtaking'] = overtaking_moran
            fig2 = self.create_moran_scatterplot(overtaking_moran, "追越会遇水域空间自相关分析")
            if fig2:
                output_file2 = self.output_dir / 'overtaking_moran_scatterplot.png'
                fig2.savefig(output_file2, dpi=300, bbox_inches='tight')
                print(f"   散点图已保存: {output_file2}")
                generated_plots['overtaking_moran'] = output_file2
                plt.close(fig2)

        return {
            'moran_results': results,
            'generated_plots': generated_plots,
            'spatial_weights': W,
            'water_mask': water_mask,
            'aggregation_used': use_aggregation,
            'final_grid_shape': grid_shape
        }

    def generate_summary_report(self, analysis_results):
        print("\n" + "=" * 60)
        print("莫兰图空间自相关分析摘要")
        print("=" * 60)
        moran_results = analysis_results['moran_results']
        generated_plots = analysis_results['generated_plots']
        for encounter_type, result in moran_results.items():
            type_name = "交叉会遇" if encounter_type == "crossing" else "追越会遇"
            print(f"\n{type_name}空间自相关:")
            print(f"  Moran's I: {result['morans_i']:.4f}")
            print(f"  期望值: {result['expected_i']:.4f}")
            print(f"  Z得分: {result['z_score']:.4f}")
            print(f"  P值: {result['p_value']:.4f}")
            if result['morans_i'] > 0:
                autocorr_type = "正空间自相关 (空间聚集)"
            elif result['morans_i'] < 0:
                autocorr_type = "负空间自相关 (空间分散)"
            else:
                autocorr_type = "无空间自相关 (随机分布)"
            print(f"  解释: {autocorr_type}")
        print(f"\n生成的莫兰散点图: {len(generated_plots)} 张")
        for key, path in generated_plots.items():
            print(f"  - {key}: {path}")
        print(f"\n📊 莫兰散点图解读：")
        print(f"   • HH (高-高): 高值被高值包围，热点区域")
        print(f"   • LL (低-低): 低值被低值包围，冷点区域")
        print(f"   • HL (高-低): 高值被低值包围，异常高值")
        print(f"   • LH (低-高): 低值被高值包围，异常低值")


def main_moran_analysis(use_aggregation=True, aggregation_factor=4):
    """
    主要莫兰分析函数（基于水域掩码）

    Args:
        use_aggregation: 是否使用网格聚合减少数据规模
        aggregation_factor: 聚合因子（4表示4x4网格聚合为1个）
    """
    print("=== 开始基于水域掩码的莫兰图空间自相关分析 ===")
    print(f"设置: 网格聚合={'开启' if use_aggregation else '关闭'}, 聚合因子={aggregation_factor}")
    print(f"水域掩码: 只分析水域网格的空间自相关模式")

    analyzer = MoranAnalysis()
    grid_results = analyzer.load_grid_results()
    if not grid_results:
        return None

    analysis_results = analyzer.analyze_spatial_autocorrelation(
        grid_results,
        use_aggregation=use_aggregation,
        aggregation_factor=aggregation_factor
    )
    if not analysis_results:
        return None

    analyzer.generate_summary_report(analysis_results)

    print(f"\n=== 莫兰图分析完成 ===")
    print(f"散点图保存位置: vis0/")
    if analysis_results.get('aggregation_used'):
        print(f"网格聚合: 最终网格大小 {analysis_results['final_grid_shape']}")

    return {
        'analysis_results': analysis_results,
        'total_plots': len(analysis_results['generated_plots'])
    }


if __name__ == '__main__':
    results_file = "result0/grid_statistics_results.pkl"
    if not os.path.exists(results_file):
        print("错误: 未找到网格统计结果文件")
        print(f"请先运行 03.grid_statistics.py 生成 {results_file}")
        print("📌 提示: 为获得正常的Z和P值，建议使用500米网格")
        exit(1)

    print("🔧 获取正常Z值和P值的解决方案:")
    print("方案1: 网格聚合 - 将当前细网格聚合为粗网格")
    print("方案2: 重新生成500米网格 - 修改03.grid_statistics.py中的网格大小")
    print("方案3: 对数变换 - 减少极值对统计的影响")
    print("\n当前使用: 网格聚合 + 对数变换的组合方案")

    # 执行改进的莫兰分析
    moran_results = main_moran_analysis(
        use_aggregation=True,  # 使用网格聚合
        aggregation_factor=4  # 4x4聚合为1个网格
    )

    if moran_results:
        print(f"\n✅ 莫兰图分析完成！")
        print(f"   生成散点图总数: {moran_results['total_plots']}")
        print(f"   查看 vis0/ 目录中的PNG文件")

        print(f"\n📈 改进效果：")
        print(f"   • 使用官方PySAL库计算，确保算法正确性")
        print(f"   • 网格聚合减少了数据稀疏性问题")
        print(f"   • 对数变换减少了极值的影响")
        print(f"   • Z值和P值应该在合理范围内")

        print(f"\n📊 结果解读：")
        print(f"   • Moran's I > 0: 正空间自相关，数据呈现空间聚集模式")
        print(f"   • Z值在±3范围内: 统计检验有效")
        print(f"   • P值 < 0.05: 空间自相关显著")
        print(f"   • 莫兰散点图展示局部空间模式的分布特征")

        print(f"\n🔄 如需进一步改善，可以:")
        print(f"   • 修改03.grid_statistics.py，使用500米网格重新生成数据")
        print(f"   • 调整aggregation_factor参数（2,4,8等）")
        print(f"   • 使用不同的数据变换方法")
    else:
        print("❌ 莫兰图分析失败")
#%%
import os
import pickle
import numpy as np
from esda.moran import Moran
from libpysal.weights import lat2W, W

class ConsistentMoranQuadrantAnalysis:
    """与完整版逻辑一致的简化象限分析器"""
    
    def __init__(self):
        pass
    
    def load_grid_results(self, results_file="result0/grid_statistics_results.pkl"):
        """加载网格统计结果"""
        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"✅ 成功加载: {results_file}")
            return results
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return None
    
    def _load_water_boundary_data(self):
        """加载水域边界数据（简化版）"""
        try:
            with open("data/geo_info.pkl", 'rb') as f:
                geo_info = pickle.load(f)
            return geo_info
        except:
            return None
    
    def _create_water_mask(self, grid_results, grid_shape):
        """创建水域掩码（简化版）"""
        geo_info = self._load_water_boundary_data()
        if geo_info is None:
            # 如果没有地理信息，使用数据驱动的掩码
            crossing_counts = grid_results['crossing_encounters']['grid_counts']
            overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
            
            # 应用聚合
            if hasattr(self, '_aggregation_factor') and self._aggregation_factor > 1:
                crossing_counts = self.aggregate_grid(crossing_counts, self._aggregation_factor)
                overtaking_counts = self.aggregate_grid(overtaking_counts, self._aggregation_factor)
            
            water_mask = (crossing_counts > 0) | (overtaking_counts > 0)
            return water_mask
        
        # 如果有地理信息，可以实现更精确的水域判断
        # 这里简化为数据驱动的方法
        crossing_counts = grid_results['crossing_encounters']['grid_counts']
        overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
        
        if hasattr(self, '_aggregation_factor') and self._aggregation_factor > 1:
            crossing_counts = self.aggregate_grid(crossing_counts, self._aggregation_factor)
            overtaking_counts = self.aggregate_grid(overtaking_counts, self._aggregation_factor)
        
        water_mask = (crossing_counts > 0) | (overtaking_counts > 0)
        return water_mask
    
    def create_water_based_weights(self, grid_shape, water_mask):
        """创建基于水域掩码的权重矩阵（与完整版一致）"""
        rows, cols = grid_shape
        
        # 创建完整网格权重矩阵
        w_full = lat2W(rows, cols, rook=True)
        
        # 获取水域网格的线性索引
        water_indices = np.where(water_mask.flatten())[0]
        n_water = len(water_indices)
        
        if n_water == 0:
            return w_full
        
        # 创建水域网格间的邻接关系
        neighbors = {}
        weights = {}
        
        # 创建索引映射
        full_to_water = {full_idx: water_idx for water_idx, full_idx in enumerate(water_indices)}
        
        for water_idx, full_idx in enumerate(water_indices):
            neighbors[water_idx] = []
            weights[water_idx] = []
            
            if full_idx in w_full.neighbors:
                for neighbor_full_idx, weight in zip(w_full.neighbors[full_idx], w_full.weights[full_idx]):
                    if neighbor_full_idx in full_to_water:
                        neighbor_water_idx = full_to_water[neighbor_full_idx]
                        neighbors[water_idx].append(neighbor_water_idx)
                        weights[water_idx].append(weight)
        
        # 创建水域权重矩阵
        w_water = W(neighbors, weights)
        w_water.transform = 'r'  # 行标准化
        
        return w_water
    
    def calculate_quadrant_statistics(self, z_values, spatial_lag_z):
        """计算象限统计"""
        total_points = len(z_values)
        
        q1_hh = np.sum((z_values > 0) & (spatial_lag_z > 0))
        q2_lh = np.sum((z_values < 0) & (spatial_lag_z > 0))
        q3_ll = np.sum((z_values < 0) & (spatial_lag_z < 0))
        q4_hl = np.sum((z_values > 0) & (spatial_lag_z < 0))
        
        return {
            'total_points': total_points,
            'q1_hh_count': q1_hh,
            'q1_hh_percent': (q1_hh / total_points) * 100,
            'q2_lh_count': q2_lh,
            'q2_lh_percent': (q2_lh / total_points) * 100,
            'q3_ll_count': q3_ll,
            'q3_ll_percent': (q3_ll / total_points) * 100,
            'q4_hl_count': q4_hl,
            'q4_hl_percent': (q4_hl / total_points) * 100,
            'q1_q3_combined_count': q1_hh + q3_ll,
            'q1_q3_combined_percent': ((q1_hh + q3_ll) / total_points) * 100
        }
    
    def aggregate_grid(self, grid_data, factor):
        """网格聚合"""
        rows, cols = grid_data.shape
        new_rows, new_cols = rows // factor, cols // factor
        aggregated = np.zeros((new_rows, new_cols))
        
        for i in range(new_rows):
            for j in range(new_cols):
                r_start, r_end = i * factor, (i + 1) * factor
                c_start, c_end = j * factor, (j + 1) * factor
                aggregated[i, j] = np.sum(grid_data[r_start:r_end, c_start:c_end])
        
        return aggregated
    
    def analyze_single_type(self, grid_counts, type_name, water_mask, W):
        """分析单一类型的会遇数据（与完整版逻辑一致）"""
        try:
            # 使用与完整版相同的逻辑
            data_flat = grid_counts.flatten()
            water_mask_flat = water_mask.flatten()
            
            # 只使用水域数据
            water_data = data_flat[water_mask_flat]
            n_water = len(water_data)
            
            if n_water < 30:
                print(f"⚠️ {type_name}: 水域数据点太少 ({n_water})")
                return None
            
            # 计算标准化值（基于水域数据）
            mean_val = np.mean(water_data)
            z_water = water_data - mean_val
            
            # 使用水域权重矩阵计算空间滞后
            wz_water = np.zeros_like(water_data)
            for i in range(len(water_data)):
                if i in W.neighbors:
                    neighbors = W.neighbors[i]
                    weights = W.weights[i]
                    if len(neighbors) > 0:
                        wz_water[i] = np.sum([weights[j] * z_water[neighbors[j]] 
                                            for j in range(len(neighbors))])
            
            # 对数变换用于莫兰计算
            data_transformed = np.log1p(water_data)
            moran = Moran(data_transformed, W)
            
            # 计算象限统计
            quad_stats = self.calculate_quadrant_statistics(z_water, wz_water)
            
            return {
                'moran_i': moran.I,
                'z_score': moran.z_norm,
                'p_value': moran.p_norm,
                'quadrant_stats': quad_stats
            }
            
        except Exception as e:
            print(f"❌ {type_name}分析失败: {e}")
            return None
    
    def analyze(self, aggregation_factor=4):
        """执行分析（与完整版逻辑一致）"""
        self._aggregation_factor = aggregation_factor
        
        # 1. 加载数据
        grid_results = self.load_grid_results()
        if not grid_results:
            return None
        
        # 2. 获取会遇数据
        crossing_counts = grid_results['crossing_encounters']['grid_counts']
        overtaking_counts = grid_results['overtaking_encounters']['grid_counts']
        
        # 3. 网格聚合
        if aggregation_factor > 1:
            crossing_counts = self.aggregate_grid(crossing_counts, aggregation_factor)
            overtaking_counts = self.aggregate_grid(overtaking_counts, aggregation_factor)
        
        grid_shape = crossing_counts.shape
        
        # 4. 创建水域掩码（与完整版逻辑一致）
        water_mask = self._create_water_mask(grid_results, grid_shape)
        
        # 5. 创建基于水域的权重矩阵（与完整版逻辑一致）
        W = self.create_water_based_weights(grid_shape, water_mask)
        
        results = {}
        
        # 6. 分析交叉会遇
        crossing_stats = self.analyze_single_type(crossing_counts, "交叉会遇", water_mask, W)
        if crossing_stats:
            results['crossing'] = crossing_stats
        
        # 7. 分析追越会遇
        overtaking_stats = self.analyze_single_type(overtaking_counts, "追越会遇", water_mask, W)
        if overtaking_stats:
            results['overtaking'] = overtaking_stats
        
        return results

def main():
    """主函数"""
    print("=== 与完整版一致的简化象限统计分析 ===")
    
    analyzer = ConsistentMoranQuadrantAnalysis()
    results = analyzer.analyze(aggregation_factor=4)
    
    if not results:
        print("❌ 分析失败")
        return
    
    # 输出结果
    print("\n📊 象限统计结果（与完整版一致）:")
    print("=" * 60)
    
    for encounter_type, data in results.items():
        type_name = "交叉会遇" if encounter_type == "crossing" else "追越会遇"
        quad_stats = data['quadrant_stats']
        
        print(f"\n{type_name}:")
        print(f"  Moran's I: {data['moran_i']:.4f}")
        print(f"  总点数: {quad_stats['total_points']}")
        print(f"  第一象限(HH): {quad_stats['q1_hh_count']} 点 ({quad_stats['q1_hh_percent']:.1f}%)")
        print(f"  第二象限(LH): {quad_stats['q2_lh_count']} 点 ({quad_stats['q2_lh_percent']:.1f}%)")
        print(f"  第三象限(LL): {quad_stats['q3_ll_count']} 点 ({quad_stats['q3_ll_percent']:.1f}%)")
        print(f"  第四象限(HL): {quad_stats['q4_hl_count']} 点 ({quad_stats['q4_hl_percent']:.1f}%)")
        print(f"  第一+三象限: {quad_stats['q1_q3_combined_count']} 点 ({quad_stats['q1_q3_combined_percent']:.1f}%)")

if __name__ == '__main__':
    if not os.path.exists("result0/grid_statistics_results.pkl"):
        print("❌ 未找到 result0/grid_statistics_results.pkl")
    else:
        main()