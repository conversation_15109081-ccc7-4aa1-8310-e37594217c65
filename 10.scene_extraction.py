import pickle
from pathlib import Path
from methods.trans import Trans
import numpy as np
from tqdm import tqdm

# 统一的内河水域会遇检测阈值配置
ENCOUNTER_THRESHOLDS = {
    'time_cross_duration': 10 * 60,  # 时间交叉条件：10分钟
    'min_distance': 300,  # 最小距离阈值：500米
    'dcpa_threshold': 300,  # DCPA阈值：500米
    'distance_threshold': 600,  # 实际距离阈值：1000米（dcpa_threshold * 2）
    'tcpa_threshold': 300,  # TCPA阈值：10分钟
    'trace_back_duration': 5 * 60,  # 追溯时间窗口：5分钟
    'min_encounter_duration': 2 * 60,  # 持续会遇时间：2分钟
    'retreat_duration': 300,  # 远离过程时间：5分钟
    'scenario_min_duration': 5 * 60,  # 场景最小持续时间：5分钟
    'data_interval': 20,  # 数据间隔：20秒
}


def calculate_encounter_parameters_multiple_memory_efficient(ships, dcpa_threshold=None, tcpa_threshold=None,
                                                             batch_size=1000):
    """
    内存友好版本：分批计算多个船只之间的会遇参数，避免创建大矩阵。
    
    :param ships: 一个包含所有船数据的列表，每个元素是一个NumPy数组，形状为 (N, 6)。
                  每列分别为 [时间, MMSI, x坐标, y坐标, 航向(角度), 速度(kn)]。
    :param dcpa_threshold: DCPA阈值，如果为None则使用默认配置
    :param tcpa_threshold: TCPA阈值，如果为None则使用默认配置
    :param batch_size: 每批处理的船只数量，默认1000
    :return: 会遇结果，包括最近会遇点和会遇形成初始时刻的动态信息
    """
    # 使用统一阈值配置
    if dcpa_threshold is None:
        dcpa_threshold = ENCOUNTER_THRESHOLDS['dcpa_threshold']
    if tcpa_threshold is None:
        tcpa_threshold = ENCOUNTER_THRESHOLDS['tcpa_threshold']

    SHIP0 = []  # 最近会遇点时的动态信息
    SHIP1 = []  # 会遇形成初始时刻的动态信息
    MM = []  # 符合会遇条件的船对记录

    # 预处理：提取时间范围和MMSI
    time_ranges = [(ship[0, 0], ship[-1, 0]) for ship in ships]
    MMSI_list = [ship[0, 1] for ship in ships]
    num_ships = len(ships)

    print(f"总船只数量: {num_ships}")
    print(f"分批大小: {batch_size}")
    print(f"预计批次数: {(num_ships * num_ships) // (batch_size * batch_size)}")

    # 分批处理
    for i_start in tqdm(range(0, num_ships, batch_size), desc="批次进度"):
        i_end = min(i_start + batch_size, num_ships)

        for j_start in range(i_start, num_ships, batch_size):
            j_end = min(j_start + batch_size, num_ships)

            # 处理当前批次内的船只对
            for i in range(i_start, i_end):
                # 避免重复计算，j从i+1开始（如果在同一批次内）或j_start开始
                j_start_actual = max(j_start, i + 1)

                for j in range(j_start_actual, j_end):
                    # 跳过相同的船只
                    if MMSI_list[i] == MMSI_list[j]:
                        continue

                    # 计算时间交集
                    t1 = max(time_ranges[i][0], time_ranges[j][0])
                    t2 = min(time_ranges[i][1], time_ranges[j][1])
                    duration = t2 - t1

                    # 检查是否满足时间交叉条件
                    if duration < ENCOUNTER_THRESHOLDS['time_cross_duration']:
                        continue

                    # 提取交叉时间段的两船数据
                    ship1 = ships[i][(ships[i][:, 0] >= t1) & (ships[i][:, 0] <= t2)]
                    ship2 = ships[j][(ships[j][:, 0] >= t1) & (ships[j][:, 0] <= t2)]

                    # 对齐时间点
                    common_times = np.intersect1d(ship1[:, 0], ship2[:, 0])
                    if len(common_times) == 0:
                        continue

                    ship1 = ship1[np.isin(ship1[:, 0], common_times)]
                    ship2 = ship2[np.isin(ship2[:, 0], common_times)]

                    # 提取位置和速度
                    x1, y1 = ship1[:, 2], ship1[:, 3]
                    x2, y2 = ship2[:, 2], ship2[:, 3]
                    dij = np.vstack((x2 - x1, y2 - y1)).T
                    Dij = np.linalg.norm(dij, axis=1)

                    v1_x = ship1[:, 5] * np.sin(np.radians(ship1[:, 4]))
                    v1_y = ship1[:, 5] * np.cos(np.radians(ship1[:, 4]))
                    v2_x = ship2[:, 5] * np.sin(np.radians(ship2[:, 4]))
                    v2_y = ship2[:, 5] * np.cos(np.radians(ship2[:, 4]))
                    vij = np.vstack((v2_x - v1_x, v2_y - v1_y)).T
                    Vij = np.linalg.norm(vij, axis=1)

                    # DCPA 和 TCPA
                    cos_b = np.einsum('ij,ij->i', dij, vij) / (Dij * Vij + 1e-8)
                    DCPA = np.abs(Dij * np.sqrt(np.abs(1 - cos_b ** 2)))
                    TCPA = -Dij / (Vij + 1e-8) * cos_b * 3600 / 1852

                    # 特殊情况处理
                    DCPA[Vij == 0] = Dij[Vij == 0]
                    TCPA[Vij == 0] = -1

                    # 判定会遇条件
                    Dis_min_idx = np.argmin(Dij)
                    Dis_min = Dij[Dis_min_idx]
                    t3 = common_times[Dis_min_idx]

                    if Dis_min <= ENCOUNTER_THRESHOLDS['min_distance'] and (t3 - t1 >= 2 * 60) and (t2 - t3 >= 2 * 60):
                        Nmin = 0
                        for t_idx in range(Dis_min_idx, max(0, Dis_min_idx - int(
                                ENCOUNTER_THRESHOLDS['trace_back_duration'] / (common_times[1] - common_times[0]))),
                                           -1):
                            if (DCPA[t_idx] < dcpa_threshold and
                                    Dij[t_idx] < ENCOUNTER_THRESHOLDS['distance_threshold'] and
                                    TCPA[t_idx] > 0 and
                                    TCPA[t_idx] <= tcpa_threshold):
                                Nmin += 1
                            else:
                                Nmin = 0

                            if Nmin * (common_times[1] - common_times[0]) >= ENCOUNTER_THRESHOLDS[
                                'min_encounter_duration']:
                                # 最近会遇点的动态信息
                                ship_YB = [
                                    ship1[ship1[:, 0] == t3][0],
                                    ship2[ship2[:, 0] == t3][0]
                                ]
                                SHIP0.append(ship_YB)

                                # 会遇形成初始时刻的动态信息
                                ship_YB1 = [
                                    ship1[ship1[:, 0] == common_times[t_idx]][0],
                                    ship2[ship2[:, 0] == common_times[t_idx]][0]
                                ]
                                SHIP1.append(ship_YB1)

                                # 记录船只索引对
                                MM.append([i, j])
                                break

    return SHIP0, SHIP1, MM


def calculate_encounter_parameters_multiple_optimized(ships, dcpa_threshold=None, tcpa_threshold=None):
    """
    优化版本：添加预筛选条件，减少需要计算的船只对数量。
    
    :param ships: 船只数据列表
    :param dcpa_threshold: DCPA阈值
    :param tcpa_threshold: TCPA阈值
    :return: 会遇结果
    """
    # 使用统一阈值配置
    if dcpa_threshold is None:
        dcpa_threshold = ENCOUNTER_THRESHOLDS['dcpa_threshold']
    if tcpa_threshold is None:
        tcpa_threshold = ENCOUNTER_THRESHOLDS['tcpa_threshold']

    SHIP0 = []
    SHIP1 = []
    MM = []

    # 预处理：提取关键信息
    ship_info = []
    for i, ship in enumerate(ships):
        info = {
            'index': i,
            'mmsi': ship[0, 1],
            't_start': ship[0, 0],
            't_end': ship[-1, 0],
            'x_min': np.min(ship[:, 2]),
            'x_max': np.max(ship[:, 2]),
            'y_min': np.min(ship[:, 3]),
            'y_max': np.max(ship[:, 3])
        }
        ship_info.append(info)

    print(f"开始处理 {len(ship_info)} 艘船只的会遇计算...")

    # 逐对计算（使用空间预筛选）
    total_pairs = len(ship_info) * (len(ship_info) - 1) // 2
    processed_pairs = 0

    with tqdm(total=total_pairs, desc="计算船只对") as pbar:
        for i in range(len(ship_info)):
            for j in range(i + 1, len(ship_info)):
                processed_pairs += 1
                pbar.update(1)

                info1, info2 = ship_info[i], ship_info[j]

                # 跳过相同MMSI
                if info1['mmsi'] == info2['mmsi']:
                    continue

                # 时间交集检查
                t1 = max(info1['t_start'], info2['t_start'])
                t2 = min(info1['t_end'], info2['t_end'])
                if t2 - t1 < ENCOUNTER_THRESHOLDS['time_cross_duration']:
                    continue

                # 空间预筛选：计算边界框距离
                x_distance = max(0, max(info1['x_min'] - info2['x_max'], info2['x_min'] - info1['x_max']))
                y_distance = max(0, max(info1['y_min'] - info2['y_max'], info2['y_min'] - info1['y_max']))
                bbox_distance = np.sqrt(x_distance ** 2 + y_distance ** 2)

                # 如果边界框距离太大，跳过详细计算
                if bbox_distance > ENCOUNTER_THRESHOLDS['distance_threshold'] * 2:
                    continue

                # 执行详细的会遇计算
                i_idx, j_idx = info1['index'], info2['index']

                # 提取交叉时间段的两船数据
                ship1 = ships[i_idx][(ships[i_idx][:, 0] >= t1) & (ships[i_idx][:, 0] <= t2)]
                ship2 = ships[j_idx][(ships[j_idx][:, 0] >= t1) & (ships[j_idx][:, 0] <= t2)]

                # 对齐时间点
                common_times = np.intersect1d(ship1[:, 0], ship2[:, 0])
                if len(common_times) == 0:
                    continue

                ship1 = ship1[np.isin(ship1[:, 0], common_times)]
                ship2 = ship2[np.isin(ship2[:, 0], common_times)]

                # 后续计算逻辑与原函数相同...
                # [这里省略重复的计算代码，与原函数中的计算逻辑相同]

                # 提取位置和速度
                x1, y1 = ship1[:, 2], ship1[:, 3]
                x2, y2 = ship2[:, 2], ship2[:, 3]
                dij = np.vstack((x2 - x1, y2 - y1)).T
                Dij = np.linalg.norm(dij, axis=1)

                v1_x = ship1[:, 5] * np.sin(np.radians(ship1[:, 4]))
                v1_y = ship1[:, 5] * np.cos(np.radians(ship1[:, 4]))
                v2_x = ship2[:, 5] * np.sin(np.radians(ship2[:, 4]))
                v2_y = ship2[:, 5] * np.cos(np.radians(ship2[:, 4]))
                vij = np.vstack((v2_x - v1_x, v2_y - v1_y)).T
                Vij = np.linalg.norm(vij, axis=1)

                # DCPA 和 TCPA
                cos_b = np.einsum('ij,ij->i', dij, vij) / (Dij * Vij + 1e-8)
                DCPA = np.abs(Dij * np.sqrt(np.abs(1 - cos_b ** 2)))
                TCPA = -Dij / (Vij + 1e-8) * cos_b * 3600 / 1852

                # 特殊情况处理
                DCPA[Vij == 0] = Dij[Vij == 0]
                TCPA[Vij == 0] = -1

                # 判定会遇条件
                Dis_min_idx = np.argmin(Dij)
                Dis_min = Dij[Dis_min_idx]
                t3 = common_times[Dis_min_idx]

                if Dis_min <= ENCOUNTER_THRESHOLDS['min_distance'] and (t3 - t1 >= 2 * 60) and (t2 - t3 >= 2 * 60):
                    Nmin = 0
                    for t_idx in range(Dis_min_idx, max(0, Dis_min_idx - int(
                            ENCOUNTER_THRESHOLDS['trace_back_duration'] / (common_times[1] - common_times[0]))), -1):
                        if (DCPA[t_idx] < dcpa_threshold and
                                Dij[t_idx] < ENCOUNTER_THRESHOLDS['distance_threshold'] and
                                TCPA[t_idx] > 0 and
                                TCPA[t_idx] <= tcpa_threshold):
                            Nmin += 1
                        else:
                            Nmin = 0

                        if Nmin * (common_times[1] - common_times[0]) >= ENCOUNTER_THRESHOLDS['min_encounter_duration']:
                            # 最近会遇点的动态信息
                            ship_YB = [
                                ship1[ship1[:, 0] == t3][0],
                                ship2[ship2[:, 0] == t3][0]
                            ]
                            SHIP0.append(ship_YB)

                            # 会遇形成初始时刻的动态信息
                            ship_YB1 = [
                                ship1[ship1[:, 0] == common_times[t_idx]][0],
                                ship2[ship2[:, 0] == common_times[t_idx]][0]
                            ]
                            SHIP1.append(ship_YB1)

                            # 记录船只索引对
                            MM.append([i_idx, j_idx])
                            break

    return SHIP0, SHIP1, MM


# 在原函数基础上添加别名，便于替换使用
def calculate_encounter_parameters_multiple(ships, dcpa_threshold=None, tcpa_threshold=None, use_memory_efficient=True):
    """
    计算多个船只之间的会遇参数的入口函数。
    
    :param ships: 船只数据列表
    :param dcpa_threshold: DCPA阈值
    :param tcpa_threshold: TCPA阈值  
    :param use_memory_efficient: 是否使用内存友好版本
    :return: 会遇结果
    """
    if use_memory_efficient:
        return calculate_encounter_parameters_multiple_optimized(ships, dcpa_threshold, tcpa_threshold)
    else:
        # 使用分批处理版本
        return calculate_encounter_parameters_multiple_memory_efficient(ships, dcpa_threshold, tcpa_threshold)


def extract_encounter_segments(SHIP0, SHIP1, MM, ships, retreat_duration=None):
    """
    提取两船会遇轨迹中包括部分远离的过程，输出符合条件的轨迹片段。

    :param SHIP0: 最近会遇点时的动态信息，每组会遇包含两船的瞬时信息
    :param SHIP1: 会遇形成初始时刻的动态信息，每组会遇包含两船的瞬时信息
    :param MM: 符合条件的船对索引
    :param ships: 所有船只轨迹的列表，每个元素是一个 NumPy 数组，形状为 (N, 6)。
                  每列分别为 [时间, MMSI, x坐标, y坐标, 航向(角度), 速度(kn)]。
    :param retreat_duration: 保留远离过程的时间（秒），如果为None则使用默认配置
    :return: SHIP2 (记录会遇轨迹信息的列表)
    """
    # 使用统一阈值配置
    if retreat_duration is None:
        retreat_duration = ENCOUNTER_THRESHOLDS['retreat_duration']
    """
    提取两船会遇轨迹中包括部分远离的过程，输出符合条件的轨迹片段。

    :param SHIP0: 最近会遇点时的动态信息，每组会遇包含两船的瞬时信息
    :param SHIP1: 会遇形成初始时刻的动态信息，每组会遇包含两船的瞬时信息
    :param MM: 符合条件的船对索引
    :param ships: 所有船只轨迹的列表，每个元素是一个 NumPy 数组，形状为 (N, 6)。
                  每列分别为 [时间, MMSI, x坐标, y坐标, 航向(角度), 速度(kn)]。
    :param retreat_duration: 保留远离过程的时间（秒）。
    :return: SHIP2 (记录会遇轨迹信息的列表)
    """
    SHIP2 = []  # 用于记录会遇轨迹片段

    for idx, (ship_idx1, ship_idx2) in enumerate(tqdm(MM, desc='step2')):
        # 获取对应的两条轨迹信息
        ship1 = ships[ship_idx1]
        ship2 = ships[ship_idx2]

        # 提取共现时间段
        t1 = max(ship1[0, 0], ship2[0, 0])  # 两轨迹的共现起始时间
        t2 = min(ship1[-1, 0], ship2[-1, 0])  # 两轨迹的共现结束时间

        # 提取会遇的初始时刻和最近点时刻
        t3 = np.array(SHIP0[idx])[0, 0]  # 最近会遇点时的时间
        t4 = np.array(SHIP1[idx])[0, 0]  # 满足会遇条件的初始时刻

        # 初始化会遇片段
        ship_KS = np.array([
            SHIP1[idx][0],  # ship1 在会遇形成初始时刻的信息
            SHIP1[idx][1],  # ship2 在会遇形成初始时刻的信息
            SHIP0[idx][0],  # ship1 在最近会遇点的信息
            SHIP0[idx][1],  # ship2 在最近会遇点的信息
        ])

        # 从 t4 往前寻找最早满足会遇条件的时间点
        for t in np.arange(t4, t1 - 1, -1):
            # 提取两船在 t 时刻的位置和速度
            ship1_t = ship1[ship1[:, 0] == t]
            ship2_t = ship2[ship2[:, 0] == t]

            if ship1_t.size == 0 or ship2_t.size == 0:
                continue

            x1, y1, v1, heading1 = ship1_t[0, 2:6]
            x2, y2, v2, heading2 = ship2_t[0, 2:6]

            # 相对位置
            di = np.array([x2 - x1, y2 - y1])
            Di = np.linalg.norm(di)

            # 相对速度
            v1_x = v1 * np.sin(np.radians(heading1))
            v1_y = v1 * np.cos(np.radians(heading1))
            v2_x = v2 * np.sin(np.radians(heading2))
            v2_y = v2 * np.cos(np.radians(heading2))
            vi = np.array([v2_x - v1_x, v2_y - v1_y])
            Vi = np.linalg.norm(vi)

            # DCPA 和 TCPA
            if Di != 0 and Vi != 0:
                cos_b = np.dot(di, vi) / (Di * Vi)
                DCPAi = abs(Di * np.sqrt(abs(1 - cos_b ** 2)))
                TCPAi = -Di / Vi * cos_b * 3600 / 1852  # 单位换算为小时
            else:
                DCPAi = Di
                TCPAi = -1

            # 判断是否满足会遇条件（使用统一阈值）
            if t == t1 or Di > ENCOUNTER_THRESHOLDS['distance_threshold'] or DCPAi > ENCOUNTER_THRESHOLDS[
                'dcpa_threshold'] or TCPAi < 0 or TCPAi > ENCOUNTER_THRESHOLDS['tcpa_threshold']:
                # 记录驶近过程的轨迹信息
                ship_KS[0] = ship1_t[0]  # 更新为当前时刻
                ship_KS[1] = ship2_t[0]  # 更新为当前时刻
                break

        # 从最近会遇点 t3 开始追加远离过程
        for t in np.arange(t3 + 1, t3 + min(retreat_duration, t2 - t3 + 1)):
            ship1_t = ship1[ship1[:, 0] == t]
            ship2_t = ship2[ship2[:, 0] == t]

            if ship1_t.size == 0 or ship2_t.size == 0:
                continue

            # 更新远离过程的轨迹信息
            ship_KS[2] = ship1_t[0]  # 更新最近会遇点 ship1 的信息
            ship_KS[3] = ship2_t[0]  # 更新最近会遇点 ship2 的信息

        # 将最终片段保存到 SHIP2
        SHIP2.append(np.copy(ship_KS))

    return SHIP2


def process_encounter_trajectories_simplified(SHIP2, MM):
    """
    简化版：直接从会遇片段提取2船会遇轨迹演化过程。
    
    :param SHIP2: 记录会遇片段的轨迹信息列表。
    :param MM: 符合条件的船对索引。
    :return: SHIP_dc1（2船会遇演化过程）
    """
    SHIP_dc1 = []  # 存储2船会遇演化过程

    for i, (ship_idx1, ship_idx2) in enumerate(tqdm(MM, desc='step3_simplified')):
        # 获取会遇时间范围
        t_start = int(SHIP2[i][0, 0])  # 会遇形成开始时间
        t_end = int(SHIP2[i][2, 0])  # 最近会遇点时间

        # 计算数据点数（每10秒一个数据点）
        row_count = (t_end - t_start) // 10 + 1

        # 创建简化的会遇演化矩阵：[时间, 会遇船数(固定为2), 本船索引, 目标船索引]
        ship_pd = np.zeros((row_count, 4))

        for idx, t in enumerate(range(t_start, t_end + 1, 10)):
            if idx < row_count:
                ship_pd[idx, 0] = t  # 时间
                ship_pd[idx, 1] = 2  # 会遇船数（固定为2）
                ship_pd[idx, 2] = ship_idx1  # 本船索引
                ship_pd[idx, 3] = ship_idx2  # 目标船索引

        SHIP_dc1.append(ship_pd)

    return SHIP_dc1


def extract_two_ship_encounters(SHIP_dc1):
    """
    简化版：直接提取2船会遇场景，不进行复杂的场景分割。
    
    :param SHIP_dc1: 2船会遇演化过程列表
    :return: 筛选出的2船会遇场景列表（持续时间超过 5 分钟）
    """
    SHIP_dc2 = []  # 存储最终的2船会遇场景

    for ship_data in tqdm(SHIP_dc1, desc='step4_simplified'):
        # 计算场景持续时间
        if len(ship_data) > 0:
            # 检查是否满足时间要求（使用统一阈值）
            min_data_points = ENCOUNTER_THRESHOLDS['scenario_min_duration'] // ENCOUNTER_THRESHOLDS['data_interval']
            if len(ship_data) >= min_data_points:
                SHIP_dc2.append(ship_data)

    return SHIP_dc2


if __name__ == '__main__':
    # 0.读取文件
    trans = Trans()
    # months = [1,2,3]
    months = [1]
    for month in tqdm(months, leave=True, desc='Month'):
        data_time = f'2024_{month}'
        with open(f'data/{data_time}_array.pkl', 'rb') as f:
            ships_array = pickle.load(f)

        # 1.提取满足会遇关系的船舶对
        Ship0, Ship1, mm = calculate_encounter_parameters_multiple(ships_array)

        # 2.提取迹对中彼此驶近的过程
        Ship2 = extract_encounter_segments(Ship0, Ship1, mm, ships_array)

        # 3.简化版：直接提取2船会遇演化过程
        Ship_dc1 = process_encounter_trajectories_simplified(Ship2, mm)

        # 4.简化版：提取2船会遇场景
        Ship_dc2 = extract_two_ship_encounters(Ship_dc1)

        # 5.保存结果
        print(f"\n=== 当前阈值配置 ===")
        for key, value in ENCOUNTER_THRESHOLDS.items():
            if 'duration' in key or 'time' in key:
                print(f"{key}: {value}秒")
            else:
                print(f"{key}: {value}")

        print(f"\n=== 处理结果统计 ===")
        print(f"识别的船舶对数量: {len(mm)}")
        print(f"提取的会遇片段数量: {len(Ship2)}")
        print(f"最终会遇场景数量: {len(Ship_dc2)}")

        print("使用简化版处理：专注于2船会遇")
        # 统计2船会遇场景的时长分布
        durations = [len(scenario) * ENCOUNTER_THRESHOLDS['data_interval'] for scenario in Ship_dc2]  # 转换为秒
        if durations:
            print(f"会遇场景平均持续时间: {np.mean(durations):.1f}秒")
            print(f"会遇场景最长持续时间: {np.max(durations):.1f}秒")
            print(f"会遇场景最短持续时间: {np.min(durations):.1f}秒")

        with open(f'data/{data_time}_array.pkl', 'wb') as f:
            pickle.dump(ships_array, f)

        # 创建结果保存的文件夹
        folder_path = Path(f"result/{data_time}")
        folder_path.mkdir(exist_ok=True)

        # 保存最终结果
        suffix = "_simplified"
        with open(f'result/{data_time}/Ship_dc2{suffix}.pkl', 'wb') as f:
            pickle.dump(Ship_dc2, f)

        print(f"结果已保存到: result/{data_time}/Ship_dc2{suffix}.pkl")
