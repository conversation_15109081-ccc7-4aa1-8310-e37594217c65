import math
import pandas as pd
import numpy as np
from scipy.optimize import leastsq
import matplotlib.pyplot as plt
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import pickle

plt.rcParams['font.family'] = 'Times New Roman'


def pickle_load(path):
    with open(path, 'rb') as f:
        return pickle.load(f)


class PSD:
    def __init__(self):
        self.a_forbidden = []
        self.b_forbidden = []
        self.a_desired = []
        self.b_desired = []

    def do(self, da, ship_type):
        print(f'current ship type:{ship_type}')
        # 1.船舶分类
        mmsi_os = self.os_get(da, ship_type)

        for i, ms_length in enumerate(tqdm(mmsi_os, desc='总进度')):
            # 2.最近距离计算
            args = [(da, ms) for ms in ms_length]
            with Pool(processes=cpu_count()) as pool:
                res_list = list(
                    tqdm(pool.imap(self.calculate_min_distances_wrapper, args), total=len(ms_length),
                         desc='最小距离计算'))
            if len(res_list) == 0:
                print(f"{i}不存在")
                continue
            res_min_distances = pd.concat(res_list, ignore_index=True)
            res_min_distances.to_parquet(f'result/{ship_type}/min_distances_{i}.parquet', index=False)

            # 3.禁止边界
            self.forbidden_boundary(res_min_distances)

            # 4.期望边界
            points_min_distances = [[xx, yy] for xx, yy in
                                    zip(list(res_min_distances['RelativeX']),
                                        list(res_min_distances['RelativeY']))]
            self.desired_boundary(self.a_forbidden[i], self.b_forbidden[i], points_min_distances)

        # 保存结果
        res = {
            'a_forbidden': self.a_forbidden,
            'b_forbidden': self.b_forbidden,
            'a_desired': self.a_desired,
            'b_desired': self.b_desired,
        }
        with open(f'result/{ship_type}/domain_{ship_type}.pkl', 'wb') as f:
            pickle.dump(res, f)

    def calculate_min_distances_wrapper(self, args):
        data, ms = args
        return self.calculate_min_distances(data, ms)

    # 本船-根据船型和大小进行划分["货船", "油轮", "客船"], [[50, 100],[100, 150],[150, 200],[200, ~]]
    @staticmethod
    def os_get(df, s_type):
        if s_type == 'h':
            s_type = '货船'
        elif s_type == 'y':
            s_type = '油轮'
        else:
            s_type = '客船'
        con = df['Type'] == s_type
        con1 = ((df['Length'] >= 50) & (df['Length'] < 100))
        con2 = ((df['Length'] >= 100) & (df['Length'] < 150))
        con3 = ((df['Length'] >= 150) & (df['Length'] < 200))
        con4 = (df['Length'] >= 200)
        mmsi_os_1 = list(set(df.loc[con & con1]['MMSI']))
        mmsi_os_2 = list(set(df.loc[con & con2]['MMSI']))
        mmsi_os_3 = list(set(df.loc[con & con3]['MMSI']))
        mmsi_os_4 = list(set(df.loc[con & con4]['MMSI']))
        return [mmsi_os_1, mmsi_os_2, mmsi_os_3, mmsi_os_4]

    # 本船行驶过程中，3海里范围内的他船距离本船的最小距离
    def calculate_min_distances(self, df, target_mmsi, distance_threshold=3 * 1852, time_threshold=60,
                                segment_interval=1200):
        target_df = df[df['MMSI'] == target_mmsi]

        if target_df.empty:
            return "No data available for the given MMSI."

        # 对轨迹进行分段
        target_df = target_df.sort_values(by='PosTime').reset_index(drop=True)
        target_df['Segment'] = (target_df['PosTime'].diff() > segment_interval).cumsum()

        results = []

        for segment, segment_df in target_df.groupby('Segment'):
            # 获取目标船的轨迹时间范围
            time_min = segment_df['PosTime'].min()
            time_max = segment_df['PosTime'].max()

            # 提前筛选时间范围内的其他船舶，并应用时间阈值
            other_ships_df = df[(df['MMSI'] != target_mmsi) &
                                (df['PosTime'] >= time_min - time_threshold) &
                                (df['PosTime'] <= time_max + time_threshold)]

            for other_mmsi, other_df in other_ships_df.groupby('MMSI'):
                # 计算相对坐标差值矩阵
                other_positions = other_df[['PosTime', 'X', 'Y']].values
                target_positions = segment_df[['PosTime', 'X', 'Y', 'Heading']].values

                min_distance = float('inf')
                min_relative_x = None
                min_relative_y = None

                # 预先使用时间阈值进行时间匹配
                for target_time, target_x, target_y, target_heading in target_positions:
                    # 计算目标船和其他船之间的时间差
                    time_diffs = np.abs(other_positions[:, 0] - target_time)

                    # 筛选出时间差在阈值范围内的点
                    valid_indices = np.where(time_diffs <= time_threshold)[0]

                    if len(valid_indices) > 0:
                        # 计算距离
                        deltas = other_positions[valid_indices, 1:3] - np.array([target_x, target_y])
                        distances = np.linalg.norm(deltas, axis=1)
                        closest_idx = np.argmin(distances)

                        current_min_distance = distances[closest_idx]

                        if current_min_distance < min_distance and current_min_distance <= distance_threshold:
                            min_distance = current_min_distance
                            relative_x, relative_y = deltas[closest_idx]

                            # 使用转换函数
                            min_relative_x, min_relative_y = self.convert_coordinates(relative_x, relative_y,
                                                                                      target_heading)

                if min_distance <= distance_threshold and min_relative_x is not None and min_relative_y is not None:
                    results.append({
                        'MinDistance': round(min_distance, 2),
                        'RelativeX': min_relative_x,
                        'RelativeY': min_relative_y,
                    })

        result_df = pd.DataFrame(results)
        return result_df

    # 禁止边界
    def forbidden_boundary(self, df):
        df['Angle'] = (np.degrees(np.arctan2(df['RelativeY'], df['RelativeX'])) + 360) % 360
        df['Sector'] = (df['Angle'] // 5).astype(int)
        # print(df)
        thresholds, critical_points = self.calculate_threshold(df)

        # 从关键点中提取X和Y坐标
        X, Y = zip(*critical_points)

        # 初始参数估计
        a_initial = (np.max(Y) - np.min(Y)) / 2
        b_initial = (np.max(X) - np.min(X)) / 2
        initial_params = [a_initial, b_initial]

        # 最小二乘法拟合
        params, _ = leastsq(self.ellipse, initial_params, args=(np.array(X), np.array(Y)))

        # 拟合结果
        a, b = params
        self.a_forbidden.append(round(float(a), 2))
        self.b_forbidden.append(round(float(b), 2))
        # self.vis_forbidden_boundary(X, Y, a, b)

    # 期望边界
    def desired_boundary(self, a0, b0, points, m=10):
        """
        处理批量椭圆数据，计算每对椭圆之间的面积差和点的比例。
        points -- [[x1,y1], [x2,y2],....,[xn,yn]]
        m -- 10米, 椭圆长半轴和短半轴的升高间隔
        a0 -- 禁止边界的长半轴
        b0 -- 禁止边界的短半轴
        """

        a = [a0 + m * k for k in range(int((1852 * 3 - a0) / m) + 1)]
        a = a + [1852 * 3]
        b = [round(b0 * ai / a0, 2) for ai in a]

        a_b = []
        ratio_between_points = []
        for i in range(len(a) - 1):
            a1, b1 = a[i], b[i]  # 小椭圆的长半轴和短半轴
            a2, b2 = a[i + 1], b[i + 1]  # 大椭圆的长半轴和短半轴
            # 计算椭圆之间的面积差
            area_between = self.calculate_area_between_ellipses(a1, b1, a2, b2)
            # 计算椭圆之间的点
            in_smaller_ellipse = np.array([self.is_point_inside_ellipse(p[0], p[1], a1, b1) for p in points])
            in_larger_ellipse = np.array([self.is_point_inside_ellipse(p[0], p[1], a2, b2) for p in points])
            points_between = np.sum(in_larger_ellipse & ~in_smaller_ellipse)
            # 计算比率
            ratio = points_between / area_between
            # 保存结果
            a_b.append((a2, b2))
            ratio_between_points.append(ratio)

        max_index = ratio_between_points.index(max(ratio_between_points))
        self.a_desired.append(float(a_b[max_index][0]))
        self.b_desired.append(float(a_b[max_index][1]))

    @staticmethod
    def convert_coordinates(relative_x, relative_y, target_heading):
        """
        将相对坐标转换到目标船（本船）的坐标系中，
        坐标系以目标船舶船头指向方向为y轴正方向。
        """
        # 将角度从度转换为弧度, 取负数是为了将艏向角转换为坐标旋转角。
        target_heading_rad = np.deg2rad(-target_heading)

        cos_angle = np.cos(target_heading_rad)
        sin_angle = np.sin(target_heading_rad)

        new_x = cos_angle * relative_x + sin_angle * relative_y
        new_y = -sin_angle * relative_x + cos_angle * relative_y

        return round(new_x, 2), round(new_y, 2)

    @staticmethod
    # 95%的置信区间, 选取各扇区前5%的点作为阈值。每个扇区5度。
    def calculate_threshold(df):
        thresholds = {}
        critical_points = []
        for sector in range(72):
            sector_data = df[df['Sector'] == sector]
            if len(sector_data) > 0:
                threshold_index = int(len(sector_data) * 0.05)
                if threshold_index > 0:
                    critical_point = sector_data.nsmallest(threshold_index, 'MinDistance').iloc[-1]
                    thresholds[sector] = critical_point['MinDistance']
                    critical_points.append((critical_point['RelativeX'], critical_point['RelativeY']))
        return thresholds, critical_points

    @staticmethod
    def ellipse(params, x, y):
        a, b = params
        return (x / b) ** 2 + (y / a) ** 2 - 1

    @staticmethod
    def is_point_inside_ellipse(x, y, a, b):
        return ((y ** 2 / a ** 2) + (x ** 2 / b ** 2)) <= 1

    @staticmethod
    def calculate_area_between_ellipses(a1, b1, a2, b2):
        area_ellipse1 = np.pi * a1 * b1
        area_ellipse2 = np.pi * a2 * b2
        return area_ellipse2 - area_ellipse1

    @staticmethod
    def plot_ellipse(a, b, center=(0, 0), ax=None, **kwargs):
        if ax is None:
            ax = plt.gca()
        theta = np.linspace(0, 2 * np.pi, 100)
        y = a * np.cos(theta) + center[0]
        x = b * np.sin(theta) + center[1]
        ax.plot(x, y, **kwargs)
        ax.fill(x, y, alpha=0.2)

    @staticmethod
    def vis_forbidden_boundary(x, y, a, b):
        # 可视化拟合结果
        fig, ax = plt.subplots()
        ax.scatter(x, y, label='Critical Points')

        # 绘制拟合的椭圆
        phi = np.linspace(0, 2 * np.pi, 360)
        x_ellipse = b * np.cos(phi)
        y_ellipse = a * np.sin(phi)
        ax.plot(x_ellipse, y_ellipse, label='Fitted Ellipse', color='red')

        ax.set_aspect('equal')
        plt.legend()
        plt.xlabel('RelativeX')
        plt.ylabel('RelativeY')
        plt.title('Ellipse Fitting (Center at Origin, Axes-Aligned)')
        plt.show()

    def vis_desired_boundary(self, a_min, b_min, a_max, b_max, res_min_distances):
        points = [[xx, yy] for xx, yy in
                  zip(list(res_min_distances['RelativeX']), list(res_min_distances['RelativeY']))]

        in_smaller_ellipse = np.array([self.is_point_inside_ellipse(p[0], p[1], a_min, b_min) for p in points])
        in_larger_ellipse = np.array([self.is_point_inside_ellipse(p[0], p[1], a_max, b_max) for p in points])
        in_larger_not_smaller = in_larger_ellipse & ~in_smaller_ellipse
        points = np.array(points)
        points_in_larger_not_smaller = points[in_larger_not_smaller]

        # 绘制点
        fig, ax = plt.subplots()
        ax.scatter(points_in_larger_not_smaller[:, 0], points_in_larger_not_smaller[:, 1], c='blue', label='Points',
                   s=10)

        # 绘制最大比率椭圆
        self.plot_ellipse(a_max, b_max, ax=ax, color='green', linestyle='-', linewidth=2, label='Desired boundary')
        # 绘制原始椭圆
        self.plot_ellipse(a_min, b_min, ax=ax, color='red', linestyle='-', linewidth=2, label='Forbidden boundary')
        ax.set_xlim(-1000, 1000)
        ax.set_aspect('equal')
        ax.legend()
        plt.xlabel('X')
        plt.ylabel('Y')
        # plt.title('Ellipses and Points')
        plt.grid(True)
        plt.show()


if __name__ == "__main__":
    ship_types = ['h', 'y', 'k']
    # ship_types = ['k']
    # data = pd.read_parquet('data/p_2023_7.parquet')
    #
    # # 执行计算
    #
    # for ship_type in ship_types:
    #     psd = PSD()
    #     psd.do(data, ship_type)

    # 可视化
    for ship_type in ship_types:
        psd = PSD()
        domain = pickle_load(f'result/{ship_type}/domain_{ship_type}.pkl')
        if ship_type in ['h', 'y']:
            for i in range(4):
                min_distances = pd.read_parquet(f'result/{ship_type}/min_distances_{i}.parquet')
                psd.vis_desired_boundary(domain['a_forbidden'][i],
                                         domain['b_forbidden'][i],
                                         domain['a_desired'][i],
                                         domain['b_desired'][i],
                                         min_distances)
        else:
            for i in range(4):
                min_distances = pd.read_parquet(f'result/{ship_type}/min_distances_{i}.parquet')
                psd.vis_desired_boundary(domain['a_forbidden'][i],
                                         domain['b_forbidden'][i],
                                         domain['a_desired'][i],
                                         domain['b_desired'][i],
                                         min_distances)
                break
