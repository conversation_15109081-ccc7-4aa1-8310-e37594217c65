import math
import os
import pickle
from pathlib import Path

import numpy as np
import pandas as pd
from tqdm import tqdm

from methods.Cross_scene_extraction import CrossSceneExtraction


class CacheManager:
    """缓存管理器"""

    def __init__(self, cache_dir="result0/cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # 数据文件路径（用于检查文件更新）
        self.data_files = [
            "data/2024_3_inter.pkl",
            "data/tras_2024_3_inter.parquet",
            "data/geo_info.pkl"
        ]

    def get_cache_path(self, step_name):
        """获取缓存文件路径"""
        return self.cache_dir / f"cache_{step_name}.pkl"

    def is_cache_valid(self, step_name):
        """检查缓存是否有效（存在且比数据文件新）"""
        cache_path = self.get_cache_path(step_name)

        if not cache_path.exists():
            return False

        cache_time = cache_path.stat().st_mtime

        # 检查是否比所有数据文件都新
        for data_file in self.data_files:
            if os.path.exists(data_file):
                data_time = os.path.getmtime(data_file)
                if cache_time < data_time:
                    return False

        return True

    def load_cache(self, step_name):
        """加载缓存"""
        cache_path = self.get_cache_path(step_name)
        with open(cache_path, 'rb') as f:
            return pickle.load(f)

    def save_cache(self, step_name, data):
        """保存缓存"""
        cache_path = self.get_cache_path(step_name)
        with open(cache_path, 'wb') as f:
            pickle.dump(data, f)
        print(f"缓存已保存: {cache_path}")

    def get_step_mapping(self):
        """获取步骤编号与缓存名称的映射"""
        return {
            1: "step1_cross_trajectories",
            2: "step2_ships_by_time",
            3: "step3a_raw_encounters",
            4: "step3b_merged_encounters"
        }

    def show_cache_status(self):
        """显示所有步骤的缓存状态"""
        step_mapping = self.get_step_mapping()
        step_descriptions = {
            1: "穿越轨迹识别",
            2: "时间片船舶数据",
            3: "原始会遇检测",
            4: "会遇合并"
        }

        print(f"\n=== 缓存状态 ===")
        for step_num in sorted(step_mapping.keys()):
            step_name = step_mapping[step_num]
            description = step_descriptions[step_num]
            cache_path = self.get_cache_path(step_name)
            status = "✅ 存在" if cache_path.exists() else "❌ 不存在"
            print(f"步骤{step_num} ({description}): {status}")

    def clear_cache_from_step(self, step_number):
        """清除指定步骤及之后的缓存"""
        step_mapping = self.get_step_mapping()

        # 清除指定步骤及之后的所有缓存
        for step_num in range(step_number, 5):  # 改为1-4步骤
            if step_num in step_mapping:
                step_name = step_mapping[step_num]
                cache_path = self.get_cache_path(step_name)
                if cache_path.exists():
                    cache_path.unlink()
                    print(f"已清除步骤{step_num}缓存: {cache_path}")


# ================================
# 基础工具函数
# ================================

def calculate_current_distance(ship1, ship2):
    """计算两船当前距离（米）"""
    R_EARTH = 6371000

    d_lon = math.radians(ship2['lon'] - ship1['lon'])
    d_lat = math.radians(ship2['lat'] - ship1['lat'])
    lat1_rad = math.radians(ship1['lat'])

    px = d_lon * R_EARTH * math.cos(lat1_rad)
    py = d_lat * R_EARTH

    return math.sqrt(px * px + py * py)


def calculate_tcpa_dcpa_accurate(ship1, ship2):
    """
    准确计算两船间的TCPA和DCPA
    
    Args:
        ship1, ship2: 船舶数据字典，包含lon, lat, cog, sog
    
    Returns:
        tcpa: 最近会遇时间（秒），负值表示已过最近点
        dcpa: 最近会遇距离（米）
    """
    # 地球半径（米）
    R_EARTH = 6371000
    KNOTS_TO_MS = 0.514444

    # 位置向量（转换为米）
    d_lon = math.radians(ship2['lon'] - ship1['lon'])
    d_lat = math.radians(ship2['lat'] - ship1['lat'])
    lat1_rad = math.radians(ship1['lat'])

    # 相对位置向量（东北坐标系，单位：米）
    px = d_lon * R_EARTH * math.cos(lat1_rad)
    py = d_lat * R_EARTH

    # 速度向量转换（船舶航向为北向顺时针角度）
    sog1_ms = ship1['sog'] * KNOTS_TO_MS
    sog2_ms = ship2['sog'] * KNOTS_TO_MS

    cog1_rad = math.radians(ship1['cog'])
    cog2_rad = math.radians(ship2['cog'])

    # 速度向量（东北坐标系）
    v1x = sog1_ms * math.sin(cog1_rad)  # 东向分量
    v1y = sog1_ms * math.cos(cog1_rad)  # 北向分量
    v2x = sog2_ms * math.sin(cog2_rad)
    v2y = sog2_ms * math.cos(cog2_rad)

    # 相对速度向量
    vrx = v2x - v1x
    vry = v2y - v1y
    vr_magnitude_sq = vrx * vrx + vry * vry

    # 如果相对速度为0，船舶相对静止
    if vr_magnitude_sq == 0:
        current_distance = math.sqrt(px * px + py * py)
        return float('inf'), current_distance

    # TCPA计算：t = -(P·Vr) / |Vr|²
    dot_product = px * vrx + py * vry
    tcpa = -dot_product / vr_magnitude_sq

    # DCPA计算：在TCPA时刻的两船距离
    # 位置预测：P(t) = P0 + Vr * t
    future_px = px + vrx * tcpa
    future_py = py + vry * tcpa
    dcpa = math.sqrt(future_px * future_px + future_py * future_py)

    return tcpa, dcpa


# ================================
# 船舶分类函数
# ================================

def classify_ships_by_crossing_status(ships_data, cross_trajectory_map, pos_time):
    """
    根据穿越状态分类船舶
    
    Args:
        ships_data: 当前时间片的船舶数据
        cross_trajectory_map: 穿越轨迹映射
        pos_time: 当前时间
    
    Returns:
        crossing_ships: 穿越船舶列表
        main_channel_ships: 主航道船舶列表
    """
    crossing_ships = []
    main_channel_ships = []

    for ship in ships_data:
        mmsi = ship['id']
        # 检查该船舶在当前时间是否正在穿越
        is_crossing = cross_trajectory_map.get((mmsi, pos_time), False)

        if is_crossing:
            crossing_ships.append(ship)
        else:
            main_channel_ships.append(ship)

    return crossing_ships, main_channel_ships


# ================================
# 航道投影相关函数
# ================================

def project_point_to_segment(ship, seg_start, seg_end):
    """将点投影到线段"""
    px, py = ship['lon'], ship['lat']
    x1, y1 = seg_start['lon'], seg_start['lat']
    x2, y2 = seg_end['lon'], seg_end['lat']

    dx = x2 - x1
    dy = y2 - y1

    if dx == 0 and dy == 0:
        distance = calculate_current_distance(ship, seg_start)
        return {
            'projection_point': (x1, y1),
            'distance': distance,
            'parameter': 0
        }

    # 投影参数
    t = ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)
    t = max(0, min(1, t))  # 限制在线段内

    # 投影点
    proj_x = x1 + t * dx
    proj_y = y1 + t * dy

    # 距离
    distance = calculate_current_distance(ship, {'lon': proj_x, 'lat': proj_y})

    return {
        'projection_point': (proj_x, proj_y),
        'distance': distance,
        'parameter': t
    }


def project_ship_to_waterway(ship, geo_information):
    """
    将船舶投影到航道中心线（简化版航道投影）
    
    Args:
        ship: 船舶数据
        geo_information: 包含航道中心线信息
    
    Returns:
        projection_info: 投影信息字典
    """
    centerline = geo_information['channel_centerline']
    ship_lon, ship_lat = ship['lon'], ship['lat']

    min_distance = float('inf')
    best_parameter = 0
    best_projection = None
    total_length = 0

    # 计算总长度
    segment_lengths = []
    for i in range(len(centerline) - 1):
        length = calculate_current_distance(
            {'lon': centerline[i][0], 'lat': centerline[i][1]},
            {'lon': centerline[i + 1][0], 'lat': centerline[i + 1][1]}
        )
        segment_lengths.append(length)
        total_length += length

    if total_length == 0:
        return {
            'parameter': 0,
            'distance_to_centerline': 0,
            'projection_point': centerline[0] if centerline else (0, 0)
        }

    cumulative_length = 0

    # 投影到每个线段
    for i in range(len(centerline) - 1):
        p1 = {'lon': centerline[i][0], 'lat': centerline[i][1]}
        p2 = {'lon': centerline[i + 1][0], 'lat': centerline[i + 1][1]}

        # 计算投影点
        projection_info = project_point_to_segment(ship, p1, p2)

        if projection_info['distance'] < min_distance:
            min_distance = projection_info['distance']
            best_projection = projection_info['projection_point']

            # 计算全局参数
            segment_parameter = projection_info['parameter']
            best_parameter = (cumulative_length + segment_parameter * segment_lengths[i]) / total_length

        cumulative_length += segment_lengths[i]

    return {
        'parameter': best_parameter,
        'distance_to_centerline': min_distance,
        'projection_point': best_projection
    }


def get_waterway_direction_at_parameter(parameter, geo_information):
    """获取航道在指定参数位置的方向"""
    centerline = geo_information['channel_centerline']

    if len(centerline) < 2:
        return 0

    # 计算参数对应的线段
    total_length = 0
    segment_lengths = []
    for i in range(len(centerline) - 1):
        length = calculate_current_distance(
            {'lon': centerline[i][0], 'lat': centerline[i][1]},
            {'lon': centerline[i + 1][0], 'lat': centerline[i + 1][1]}
        )
        segment_lengths.append(length)
        total_length += length

    if total_length == 0:
        return 0

    target_length = parameter * total_length
    cumulative_length = 0

    for i in range(len(centerline) - 1):
        if cumulative_length + segment_lengths[i] >= target_length:
            # 在这个线段上
            lon1, lat1 = centerline[i]
            lon2, lat2 = centerline[i + 1]

            direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
            if direction < 0:
                direction += 360
            return direction

        cumulative_length += segment_lengths[i]

    # 默认使用最后一段的方向
    lon1, lat1 = centerline[-2]
    lon2, lat2 = centerline[-1]
    direction = math.degrees(math.atan2(lon2 - lon1, lat2 - lat1))
    if direction < 0:
        direction += 360
    return direction


def determine_ship_direction_on_waterway(ship, geo_information):
    """判断船舶在航道上的行驶方向"""
    projection_info = project_ship_to_waterway(ship, geo_information)
    parameter = projection_info['parameter']

    # 获取航道在该位置的方向
    waterway_direction = get_waterway_direction_at_parameter(parameter, geo_information)

    # 计算船舶航向与航道方向的夹角
    ship_heading = ship['cog']
    angle_diff = abs(ship_heading - waterway_direction)
    angle_diff = min(angle_diff, 360 - angle_diff)

    # 判断方向：夹角小于90度为下行，大于90度为上行
    return 'downstream' if angle_diff < 90 else 'upstream'


def determine_ship_side_on_waterway(ship, geo_information):
    """判断船舶在航道的左侧还是右侧"""
    projection_info = project_ship_to_waterway(ship, geo_information)
    projection_point = projection_info['projection_point']
    parameter = projection_info['parameter']

    # 获取航道方向
    waterway_direction_deg = get_waterway_direction_at_parameter(parameter, geo_information)
    waterway_direction_rad = math.radians(waterway_direction_deg)

    # 航道方向向量
    waterway_dx = math.sin(waterway_direction_rad)
    waterway_dy = math.cos(waterway_direction_rad)

    # 从投影点到船舶的向量
    ship_dx = ship['lon'] - projection_point[0]
    ship_dy = ship['lat'] - projection_point[1]

    # 向量叉积判断左右
    cross_product = waterway_dx * ship_dy - waterway_dy * ship_dx

    return 'right' if cross_product > 0 else 'left'


# ================================
# 会遇检测函数
# ================================

def detect_crossing_encounters(crossing_ships, main_channel_ships, pos_time,
                               tcpa_threshold=300, dcpa_threshold=300):
    encounters = []

    for crossing_ship in crossing_ships:  # ✅ 正确：遍历穿越船舶
        for main_ship in main_channel_ships:  # ✅ 正确：遍历主航道船舶
            # 计算TCPA和DCPA
            tcpa, dcpa = calculate_tcpa_dcpa_accurate(crossing_ship, main_ship)

            # 判断是否构成有效会遇
            if 0 <= tcpa <= tcpa_threshold and dcpa <= dcpa_threshold:
                # 创建标准化的船舶对
                s_pair = tuple(sorted([crossing_ship['id'], main_ship['id']]))

                encounters.append({
                    's_pair': s_pair,
                    'pos_time': pos_time,
                    'tcpa': tcpa,
                    'dcpa': dcpa,
                    'current_distance': calculate_current_distance(crossing_ship, main_ship),
                    'encounter_type': 'crossing'
                })

    return encounters


def detect_overtaking_encounters(main_channel_ships, geo_information, pos_time,
                                 tcpa_threshold=300, dcpa_threshold=100):  # 降低到100米
    """
    检测主航道船舶间的追越会遇
    保持航道投影的核心逻辑和优化同侧同向判断
    
    Args:
        main_channel_ships: 主航道船舶列表
        geo_information: 航道地理信息
        pos_time: 当前时间
        tcpa_threshold: TCPA阈值（秒）
        dcpa_threshold: DCPA阈值（米）
    
    Returns:
        encounters: 追越会遇列表
    """
    encounters = []

    if len(main_channel_ships) < 2:
        return encounters

    # 为每艘船添加航道投影信息
    ships_with_projection = []
    for ship in main_channel_ships:
        ship_copy = ship.copy()
        projection_info = project_ship_to_waterway(ship, geo_information)
        ship_copy.update(projection_info)
        ship_copy['side'] = determine_ship_side_on_waterway(ship, geo_information)
        ship_copy['direction'] = determine_ship_direction_on_waterway(ship, geo_information)
        ships_with_projection.append(ship_copy)

    # 按侧别和方向分组
    groups = {}
    for ship in ships_with_projection:
        key = f"{ship['side']}_{ship['direction']}"
        if key not in groups:
            groups[key] = []
        groups[key].append(ship)

    # 在每个组内检测追越会遇
    for group_key, group_ships in groups.items():
        if len(group_ships) < 2:
            continue

        # 按航道位置排序
        group_ships.sort(key=lambda x: x['parameter'])

        # 检测相邻船舶的追越关系
        for i in range(len(group_ships)):
            for j in range(i + 1, len(group_ships)):
                ship1 = group_ships[i]
                ship2 = group_ships[j]

                # 计算TCPA和DCPA
                tcpa, dcpa = calculate_tcpa_dcpa_accurate(ship1, ship2)

                # 判断是否构成追越会遇
                if 0 <= tcpa <= tcpa_threshold and dcpa <= dcpa_threshold:
                    # 确认是追越关系：后船速度大于前船
                    parameter_diff = ship2['parameter'] - ship1['parameter']
                    speed_diff = ship2['sog'] - ship1['sog']

                    # 追越条件：位置在后且速度更快
                    if parameter_diff > 0 and speed_diff > 0.5:  # 速度差至少0.5节
                        s_pair = tuple(sorted([ship1['id'], ship2['id']]))

                        encounters.append({
                            's_pair': s_pair,
                            'pos_time': pos_time,
                            'tcpa': tcpa,
                            'dcpa': dcpa,
                            'current_distance': calculate_current_distance(ship1, ship2),
                            'encounter_type': 'overtaking'
                        })

    return encounters


# ================================
# 主要处理流程函数
# ================================

def load_basic_data():
    """步骤0: 加载基础数据"""
    print("正在加载数据...")
    trajectory_list = pickle.load(open("data/2024_3_inter.pkl", "rb"))
    tras_df = pd.read_parquet('data/tras_2024_3_inter.parquet')

    with open('data/geo_info.pkl', 'rb') as f:
        geo_information = pickle.load(f)

    return trajectory_list, tras_df, geo_information


def extract_cross_trajectories(trajectory_list, tras_df, geo_information, cache_manager, use_cache=True):
    """步骤1: 识别穿越轨迹片段"""
    step_name = "step1_cross_trajectories"

    if use_cache and cache_manager.is_cache_valid(step_name):
        print("从缓存加载穿越轨迹识别结果...")
        return cache_manager.load_cache(step_name)

    print("正在识别穿越轨迹片段...")
    cross_extractor = CrossSceneExtraction(trajectory_list, tras_df, geo_information)
    cross_ship_indices = cross_extractor.cross_owns_indentify()

    # 建立穿越轨迹的时空映射：(MMSI, 时间段) -> 是否穿越
    cross_trajectory_map = {}
    cross_mmsi_count = set()

    for idx in cross_ship_indices:
        if idx < len(trajectory_list):
            trajectory = trajectory_list[idx]
            mmsi = trajectory['MMSI'].iloc[0]

            # 为该轨迹片段的每个时间点标记为穿越
            for _, row in trajectory.iterrows():
                pos_time = row['PosTime']
                cross_trajectory_map[(mmsi, pos_time)] = True

            cross_mmsi_count.add(mmsi)

    result = {
        'cross_ship_indices': cross_ship_indices,
        'cross_trajectory_map': cross_trajectory_map,
        'cross_mmsi_count': cross_mmsi_count
    }

    print(f"发现 {len(cross_ship_indices)} 条穿越轨迹片段，涉及 {len(cross_mmsi_count)} 艘船舶")

    if use_cache:
        cache_manager.save_cache(step_name, result)

    return result


def build_ships_by_time(tras_df, cache_manager, use_cache=True):
    """步骤2: 构建时间片船舶数据"""
    step_name = "step2_ships_by_time"

    if use_cache and cache_manager.is_cache_valid(step_name):
        print("从缓存加载时间片船舶数据...")
        return cache_manager.load_cache(step_name)

    print("正在构建时间片船舶数据...")
    ships_by_time = {}
    grouped = tras_df.groupby('PosTime')

    for pos_time, group in tqdm(grouped, desc='构建船舶数据'):
        ships_data = []
        for _, row in group.iterrows():
            ships_data.append({
                'PosTime': pos_time,
                'id': int(row['MMSI']),
                'lon': float(row['Lon']),
                'lat': float(row['Lat']),
                'cog': float(row['Cog']),
                'sog': float(row['Sog'])
            })
        ships_by_time[pos_time] = ships_data

    if use_cache:
        cache_manager.save_cache(step_name, ships_by_time)

    return ships_by_time


def detect_raw_encounters(ships_by_time, cross_trajectory_map, geo_information, cache_manager, use_cache=True):
    """
    步骤3A: 检测原始会遇事件
    重新实现：分别处理交叉会遇和追越会遇
    """
    step_name = "step3a_raw_encounters"

    if use_cache and cache_manager.is_cache_valid(step_name):
        print("从缓存加载原始会遇检测结果...")
        return cache_manager.load_cache(step_name)

    print("正在检测原始会遇...")

    raw_encounters = []
    crossing_count = 0
    overtaking_count = 0

    for pos_time, ships_data in tqdm(ships_by_time.items(), desc='检测时间片会遇'):
        # 1. 识别穿越船舶和主航道船舶
        crossing_ships, main_channel_ships = classify_ships_by_crossing_status(
            ships_data, cross_trajectory_map, pos_time)

        # 2. 检测穿越-主航道的交叉会遇
        if crossing_ships and main_channel_ships:
            crossing_encounters = detect_crossing_encounters(
                crossing_ships, main_channel_ships, pos_time)
            raw_encounters.extend(crossing_encounters)
            crossing_count += len(crossing_encounters)

        # 3. 检测主航道内的追越会遇
        if len(main_channel_ships) >= 2:
            overtaking_encounters = detect_overtaking_encounters(
                main_channel_ships, geo_information, pos_time)
            raw_encounters.extend(overtaking_encounters)
            overtaking_count += len(overtaking_encounters)

    print(f"原始会遇事件数量: {len(raw_encounters)}")
    print(f"  - 交叉会遇: {crossing_count}")
    print(f"  - 追越会遇: {overtaking_count}")

    if use_cache:
        cache_manager.save_cache(step_name, raw_encounters)

    return raw_encounters


def merge_encounters(raw_encounters, cache_manager, use_cache=True, max_gap_minutes=2, min_segments=15):
    """步骤3B: 合并连续的会遇过程"""
    step_name = "step3b_merged_encounters"

    if use_cache and cache_manager.is_cache_valid(step_name):
        print("从缓存加载会遇合并结果...")
        return cache_manager.load_cache(step_name)

    print("正在合并连续的会遇过程...")
    merged_encounters = _merge_continuous_encounters(raw_encounters, max_gap_minutes=max_gap_minutes, min_segments=min_segments)

    print(f"合并后会遇过程数量: {len(merged_encounters)}")
    if len(raw_encounters) > 0:
        print(f"合并比例: {len(merged_encounters) / len(raw_encounters) * 100:.2f}% (合并后/原始)")

    # 统计持续时间
    durations = [enc.get('duration_seconds', 0) for enc in merged_encounters]
    if durations:
        print(f"平均持续时间: {np.mean(durations):.1f}秒")
        print(f"最长持续时间: {max(durations)}秒")
        print(f"持续时间>0的会遇: {sum(1 for d in durations if d > 0)}")

    if use_cache:
        cache_manager.save_cache(step_name, merged_encounters)

    return merged_encounters


# ================================
# 合并辅助函数
# ================================

def _merge_continuous_encounters(raw_encounters, max_gap_minutes=2, min_segments=15):
    """
    基于时间间隔简化合并连续的会遇过程
    修改：时间间隔阈值改为2分钟，至少15个连续片段才认为是有效会遇
    """
    if not raw_encounters:
        return []

    # 按船舶对分组
    encounters_by_pair = {}
    for encounter in raw_encounters:
        s_pair = encounter['s_pair']
        if s_pair not in encounters_by_pair:
            encounters_by_pair[s_pair] = []
        encounters_by_pair[s_pair].append(encounter)

    merged_encounters = []
    max_gap_seconds = max_gap_minutes * 60  # 2分钟 = 120秒

    # 处理每个船舶对
    for s_pair, pair_encounters in encounters_by_pair.items():
        # 按时间排序
        pair_encounters.sort(key=lambda x: x['pos_time'])

        # 基于时间间隔简化合并，要求至少6个连续片段
        complete_encounters = _merge_by_time_interval_only(pair_encounters, max_gap_seconds, min_segments)

        # 添加到结果中
        merged_encounters.extend(complete_encounters)

    # 按代表性时间排序
    merged_encounters.sort(key=lambda x: x['pos_time'])

    return merged_encounters


def _merge_by_time_interval_only(pair_encounters, max_gap_seconds, min_segments):
    """
    仅基于时间间隔进行会遇合并，要求至少指定数量的连续片段
    
    Args:
        pair_encounters: 同一船舶对的会遇检测事件列表
        max_gap_seconds: 最大时间间隔（秒）
        min_segments: 最小片段数量要求
    
    Returns:
        合并后的会遇过程列表
    """
    if len(pair_encounters) < min_segments:
        return []

    merged_encounters = []

    # 将连续的检测点分组（基于时间间隔）
    time_groups = _group_by_time_continuity_simplified(pair_encounters, max_gap_seconds, min_segments)

    # 对每个符合要求的时间组，创建会遇过程
    for time_group in time_groups:
        encounter_process = _create_encounter_process_simplified(time_group, min_segments)
        if encounter_process:
            merged_encounters.append(encounter_process)

    return merged_encounters


def _create_encounter_process_simplified(encounters, min_segments=15):
    """
    为时间连续的检测片段创建简化的会遇过程
    
    Args:
        encounters: 连续的会遇检测事件列表
        min_segments: 最小片段数量要求
    
    Returns:
        会遇过程字典
    """
    if len(encounters) < min_segments:  # 确保至少指定数量的片段
        return None

    # 找到DCPA最小的点作为最近会遇点
    min_dcpa_idx = min(range(len(encounters)), key=lambda i: encounters[i]['dcpa'])

    s_pair = encounters[0]['s_pair']
    times = [enc['pos_time'] for enc in encounters]
    start_time = times[0]
    end_time = times[-1]
    closest_approach_time = times[min_dcpa_idx]

    encounter_type = encounters[0].get('encounter_type', 'unknown')

    # 提取序列数据
    tcpa_sequence = [enc['tcpa'] for enc in encounters]
    dcpa_sequence = [enc['dcpa'] for enc in encounters]
    distance_sequence = [enc['current_distance'] for enc in encounters]

    return {
        's_pair': list(s_pair),
        'pos_time': closest_approach_time,
        'start_time': start_time,
        'end_time': end_time,
        'encounter_times': times,
        'duration_seconds': end_time - start_time,
        'segment_count': len(encounters),  # 添加片段数量信息
        'min_tcpa': encounters[min_dcpa_idx]['tcpa'],
        'min_dcpa': encounters[min_dcpa_idx]['dcpa'],
        'encounter_type': encounter_type,
        'tcpa_sequence': tcpa_sequence,
        'dcpa_sequence': dcpa_sequence,
        'distance_sequence': distance_sequence
    }


def _group_by_time_continuity_simplified(encounters, max_gap_seconds, min_segments=15):
    """
    基于时间连续性将检测事件分组 - 简化版本，要求至少指定数量片段
    
    Args:
        encounters: 检测事件列表
        max_gap_seconds: 最大时间间隔（秒）
        min_segments: 最小片段数量要求
    
    Returns:
        符合要求的时间组列表
    """
    if not encounters:
        return []

    groups = []
    current_group = [encounters[0]]

    for i in range(1, len(encounters)):
        time_gap = encounters[i]['pos_time'] - encounters[i - 1]['pos_time']

        if time_gap <= max_gap_seconds:
            # 时间间隔符合要求，添加到当前组
            current_group.append(encounters[i])
        else:
            # 时间间隔过大，结束当前组
            if len(current_group) >= min_segments:
                groups.append(current_group)
            current_group = [encounters[i]]

    # 处理最后一组
    if len(current_group) >= min_segments:
        groups.append(current_group)

    return groups


# ================================
# 主函数 - 会遇提取流程
# ================================

def extract_encounters(use_cache=True, force_recalculate_from_step=None, max_gap_minutes=2, min_segments=15):
    """
    会遇提取主函数
    
    Args:
        use_cache: 是否使用缓存，默认True
        force_recalculate_from_step: 强制从指定步骤开始重新计算，默认None
        min_segments: 最小片段数量要求，默认6
    
    Returns:
        dict: 包含会遇数据和基础信息的字典
    """
    # 初始化缓存管理器
    cache_manager = CacheManager()

    # 显示缓存状态
    if use_cache:
        cache_manager.show_cache_status()

    # 如果指定强制重新计算，清除相应缓存
    if force_recalculate_from_step is not None:
        print(f"\n强制从步骤 {force_recalculate_from_step} 开始重新计算...")
        cache_manager.clear_cache_from_step(force_recalculate_from_step)

    # 步骤0: 加载基础数据
    trajectory_list, tras_df, geo_information = load_basic_data()

    # 步骤1: 识别穿越轨迹片段
    cross_result = extract_cross_trajectories(trajectory_list, tras_df, geo_information, cache_manager, use_cache)
    cross_trajectory_map = cross_result['cross_trajectory_map']
    cross_mmsi_count = cross_result['cross_mmsi_count']

    # 步骤2: 构建时间片船舶数据
    ships_by_time = build_ships_by_time(tras_df, cache_manager, use_cache)

    # 步骤3A: 检测原始会遇
    raw_encounters = detect_raw_encounters(ships_by_time, cross_trajectory_map, geo_information, cache_manager,
                                           use_cache)

    # 步骤3B: 合并会遇过程
    merged_encounters = merge_encounters(raw_encounters, cache_manager, use_cache, max_gap_minutes=max_gap_minutes, min_segments=min_segments)

    print(f"最终会遇数量: {len(merged_encounters)}")

    # 直接按encounter_type分类
    cross_related_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'crossing']
    non_cross_encounters = [enc for enc in merged_encounters if enc.get('encounter_type') == 'overtaking']

    print(f"交叉会遇: {len(cross_related_encounters)}")
    print(f"追越会遇: {len(non_cross_encounters)}")

    # 返回提取结果
    extraction_results = {
        'encounters': merged_encounters,
        'cross_related_encounters': cross_related_encounters,
        'non_cross_encounters': non_cross_encounters,
        'tras_df': tras_df,
        'cross_trajectory_map': cross_trajectory_map,
        'cross_mmsi_count': cross_mmsi_count,
        'summary': {
            'total_encounters': len(merged_encounters),
            'cross_related_count': len(cross_related_encounters),
            'non_cross_count': len(non_cross_encounters),
            'cross_trajectory_count': len([k for k in cross_trajectory_map.keys()]),
            'cross_ship_count': len(cross_mmsi_count)
        }
    }

    print(f"会遇提取完成！")
    return extraction_results


if __name__ == '__main__':
    # 直接执行会遇提取
    print("=== 开始船舶会遇提取 ===")
    print("参数设置:")
    print("  - 使用缓存: True")
    
    results = extract_encounters(use_cache=True, max_gap_minutes=5, min_segments=6)
    
    # 保存完整的提取结果到result1目录
    print("\n正在保存完整提取结果...")
    import os
    os.makedirs("result0", exist_ok=True)
    
    output_file = "result0/encounter_extraction_results.pkl"
    with open(output_file, 'wb') as f:
        pickle.dump(results, f)
    
    print(f"完整结果已保存到: {output_file}")
    
    # 输出最终总结
    print(f"\n=== 会遇提取完成 ===")
    print(f"总会遇过程: {results['summary']['total_encounters']}")
    print(f"  - 交叉会遇: {results['summary']['cross_related_count']}")
    print(f"  - 追越会遇: {results['summary']['non_cross_count']}")
    
    # 统计持续时间信息
    durations = [enc.get('duration_seconds', 0) for enc in results['encounters']]
    if durations:
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        print(f"\n会遇过程持续时间:")
        print(f"  - 平均持续: {avg_duration:.1f}秒 ({avg_duration/60:.1f}分钟)")
        print(f"  - 最长持续: {max_duration}秒 ({max_duration/60:.1f}分钟)")
    
    print(f"\n数据文件:")
    print(f"  - 缓存文件: result0/cache/cache_step3b_merged_encounters.pkl")
    print(f"  - 完整结果: {output_file}")
    print(f"\n可以运行以下模块进行后续分析:")
    print(f"  📊 python 02.number_statistics.py")
    print(f"     └─ 输出: number_statistics_results.pkl + 3个CSV文件")
    print(f"  🗺️  python 03.grid_statistics.py")  
    print(f"     └─ 输出: grid_statistics_results.pkl + CSV文件")
