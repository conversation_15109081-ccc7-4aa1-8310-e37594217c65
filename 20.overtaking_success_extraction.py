"""
追越成功场景提取系统
基于12.avoidance_scene_extraction.py的避让场景提取结果，进一步提取追越成功的场景

流程：
1. 模仿12.avoidance_scene_extraction.py提取追越场景（排除交叉场景）
2. 分析追越场景中两艘船的相对位置变化
3. 识别位置发生变化的时刻（从落后变为领先）
4. 保存追越成功场景和完整追越场景

输出文件：
- result/plot_data/{data_time}/overtaking_success_scenes.pkl: 追越成功场景（包含完整场景信息）
- result/plot_data/{data_time}/overtaking_full_scenes.pkl: 完整的追越场景（用于后续分析）

数据结构：
每个追越成功场景包含：
- 场景开始时间
- 场景结束时间
- 追越船MMSI
- 被追越船MMSI
- 位置变化时刻
- 两船在变化时刻的状态信息
- 完整的场景轨迹信息
"""

import pickle
import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict
import warnings

warnings.filterwarnings('ignore')

from methods.Cross_scene_extraction import CrossSceneExtraction
from methods.Ship_maneuvering_behavior_recognition import CorrectedZeroCrossingDetector
from methods.trans import Trans


class OvertakingSuccessExtractor:
    """追越成功场景提取器"""

    def __init__(self, data_time='2024_3'):
        """
        初始化追越成功场景提取器
        
        :param data_time: 数据时间标识
        """
        self.data_time = data_time

        # 加载基础数据
        print("正在加载基础数据...")
        self._load_basic_data()

        # 初始化组件
        self.cross_extractor = CrossSceneExtraction(self.trajectory_list, self.tras_df, self.geo_information)
        self.maneuvering_detector = CorrectedZeroCrossingDetector(magnitude_percentile=75)

        # 缓存变量
        self.crossing_ship_indices = None
        self.crossing_ship_mmsi_set = None
        self.maneuvering_index = {}

    def _load_basic_data(self):
        """加载基础数据"""
        # 加载轨迹数据
        with open(f'data/{self.data_time}_inter.pkl', 'rb') as f:
            self.trajectory_list = pickle.load(f)

        # 加载tras_df数据
        self.tras_df = pd.read_parquet(f'data/tras_{self.data_time}_inter.parquet')

        # 加载地理信息
        with open('data/geo_info.pkl', 'rb') as f:
            self.geo_information = pickle.load(f)

        # 转换地理信息中的经纬度点列表为Shapely几何对象
        self._convert_geo_objects()

        print(f"✅ 基础数据加载完成")
        print(f"   轨迹数据: {len(self.trajectory_list)} 艘船")
        print(f"   轨迹点数据: {len(self.tras_df)} 条记录")

    def _convert_geo_objects(self):
        """将地理信息中的经纬度点列表转换为Shapely几何对象"""
        from shapely.geometry import Polygon

        print("正在转换地理对象...")

        # 需要转换的字段列表
        geo_fields = ['channel_boundary', 'anchorage',
                      'subregion_1', 'subregion_2', 'subregion_3', 'subregion_4']

        converted_count = 0

        for field in geo_fields:
            if field in self.geo_information:
                points = self.geo_information[field]

                # 检查是否是点列表格式
                if isinstance(points, list) and len(points) > 0:
                    try:
                        # 检查第一个元素是否是坐标点
                        if isinstance(points[0], (tuple, list)) and len(points[0]) == 2:
                            # 转换为Polygon对象
                            if len(points) >= 3:  # 多边形至少需要3个点
                                polygon = Polygon(points)
                                self.geo_information[field] = polygon
                                converted_count += 1
                                print(f"✅ {field}: {len(points)}个点 -> Polygon对象")
                            else:
                                print(f"⚠️  {field}: 点数不足，无法构成多边形")
                        else:
                            print(f"⚠️  {field}: 数据格式不正确")
                    except Exception as e:
                        print(f"❌ {field}: 转换失败 - {e}")
                else:
                    print(f"⚠️  {field}: 不是有效的点列表")

        print(f"✅ 地理对象转换完成，共转换 {converted_count} 个对象")

    def step1_load_encounter_scenes(self):
        """Step 1: 加载会遇场景"""
        print("\n=== Step 1: 加载会遇场景 ===")

        scene_file = f'result/{self.data_time}/Ship_dc2_simplified.pkl'
        if not Path(scene_file).exists():
            scene_file = f'result/{self.data_time}/Ship_dc2.pkl'

        if not Path(scene_file).exists():
            raise FileNotFoundError(f"未找到会遇场景文件: {scene_file}")

        with open(scene_file, 'rb') as f:
            self.encounter_scenes = pickle.load(f)

        print(f"✅ 已加载 {len(self.encounter_scenes)} 个会遇场景")
        return self.encounter_scenes

    def step2_identify_crossing_ships(self):
        """Step 2: 识别穿越船舶"""
        print("\n=== Step 2: 识别穿越船舶 ===")

        # 使用CrossSceneExtraction识别穿越船
        self.crossing_ship_indices = self.cross_extractor.cross_owns_indentify()

        # 获取穿越船的时间段信息：[(mmsi, start_time, end_time), ...]
        self.crossing_ship_time_segments = []
        self.crossing_ship_mmsi_set = set()

        for idx in self.crossing_ship_indices:
            if idx < len(self.trajectory_list):
                trajectory = self.trajectory_list[idx]
                mmsi = trajectory['MMSI'].iloc[0]
                start_time = trajectory['PosTime'].iloc[0]
                end_time = trajectory['PosTime'].iloc[-1]

                self.crossing_ship_time_segments.append((mmsi, start_time, end_time))
                self.crossing_ship_mmsi_set.add(mmsi)

        print(f"✅ 识别出 {len(self.crossing_ship_mmsi_set)} 艘穿越船舶")
        print(f"   穿越轨迹段数: {len(self.crossing_ship_indices)}")
        print(f"   穿越时间段数: {len(self.crossing_ship_time_segments)}")

    def step3_classify_encounter_scenes(self):
        """Step 3: 分类会遇场景（只保留追越场景，排除交叉场景）"""
        print("\n=== Step 3: 分类会遇场景 ===")

        self.overtaking_scenes = []
        self.unclassified_scenes = []

        # 预构建优化索引
        self._build_optimization_indices()

        debug_stats = {
            'has_crossing': 0,
            'both_in_channel': 0,
            'position_not_found': 0,
            'total_checked': 0
        }

        for scene in tqdm(self.encounter_scenes, desc="场景分类"):
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])

            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            scene_times = np.unique(scene[:, 0])
            scene_start_time = int(scene_times[0])
            scene_end_time = int(scene_times[-1])

            debug_stats['total_checked'] += 1

            # 使用优化的穿越船检查
            is_crossing = self._has_crossing_ship_optimized(
                ship1_mmsi, ship2_mmsi, scene_start_time, scene_end_time
            )

            if is_crossing:
                debug_stats['has_crossing'] += 1
                # 排除交叉场景，不保存
                continue
            else:
                # 使用缓存的位置查询
                ship1_pos = self._get_ship_position_cached(ship1_mmsi, scene_start_time)
                ship2_pos = self._get_ship_position_cached(ship2_mmsi, scene_start_time)

                if ship1_pos is None or ship2_pos is None:
                    debug_stats['position_not_found'] += 1
                    self.unclassified_scenes.append(scene)
                elif self._both_ships_in_main_channel_cached(ship1_mmsi, ship2_mmsi, scene_start_time):
                    debug_stats['both_in_channel'] += 1
                    self.overtaking_scenes.append(scene)
                else:
                    self.unclassified_scenes.append(scene)

        # 显示统计信息
        print(f"\n   调试统计:")
        print(f"   ├─ 总检查场景: {debug_stats['total_checked']}")
        print(f"   ├─ 有穿越船参与: {debug_stats['has_crossing']} (已排除)")
        print(f"   ├─ 都在主航道: {debug_stats['both_in_channel']}")
        print(f"   └─ 位置信息缺失: {debug_stats['position_not_found']}")

        print(f"✅ 场景分类完成:")
        print(f"   追越场景: {len(self.overtaking_scenes)} 个")
        print(f"   未分类场景: {len(self.unclassified_scenes)} 个")

    def step4_filter_overtaking_by_subregion(self):
        """Step 4: 按子航道筛选追越场景"""
        print("\n=== Step 4: 按子航道筛选追越场景 ===")

        # 检查是否有子区域定义
        subregions = {}
        for i in range(1, 5):
            subregion_key = f'subregion_{i}'
            if subregion_key in self.geo_information:
                subregions[subregion_key] = self.geo_information[subregion_key]

        if not subregions:
            print("⚠️  未找到子区域定义，保留所有追越场景")
            self.filtered_overtaking_scenes = self.overtaking_scenes
            return

        print(f"✅ 找到 {len(subregions)} 个子区域: {list(subregions.keys())}")

        self.filtered_overtaking_scenes = []

        for scene in tqdm(self.overtaking_scenes, desc="子航道筛选"):
            # 获取场景中第一个时间点的船舶位置
            scene_times = np.unique(scene[:, 0])
            time_point = int(scene_times[0])

            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])

            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            # 获取两船的位置信息
            ship1_pos = self._get_ship_position(ship1_mmsi, time_point)
            ship2_pos = self._get_ship_position(ship2_mmsi, time_point)

            if ship1_pos is None or ship2_pos is None:
                continue

            # 判断两船是否在同一子区域
            ship1_subregion = self._get_ship_subregion(ship1_pos, subregions)
            ship2_subregion = self._get_ship_subregion(ship2_pos, subregions)

            if ship1_subregion is not None and ship1_subregion == ship2_subregion:
                self.filtered_overtaking_scenes.append(scene)

        print(f"✅ 子航道筛选完成:")
        print(f"   筛选前追越场景: {len(self.overtaking_scenes)} 个")
        print(f"   筛选后追越场景: {len(self.filtered_overtaking_scenes)} 个")

    def step5_identify_maneuvering_behaviors(self):
        """Step 5: 识别机动行为"""
        print("\n=== Step 5: 识别机动行为 ===")

        # 获取所有需要检查机动行为的船舶
        all_idx_set = set()
        for scene in self.filtered_overtaking_scenes:
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])

            all_idx_set.add(ship1_idx)
            all_idx_set.add(ship2_idx)

        print(f"需要检查机动行为的船舶数量: {len(all_idx_set)}")

        # 为每艘船识别机动行为
        for idx in tqdm(all_idx_set, desc="机动行为识别"):
            mmsi = self.mmsi_cache[idx]
            try:
                # 获取该船舶的轨迹数据
                ship_trajectory = self._get_ship_trajectory(idx)
                if ship_trajectory is None or len(ship_trajectory) < 10:
                    continue

                # 进行机动行为检测
                result = self.maneuvering_detector.detect_maneuvers(ship_trajectory, debug=False)
                maneuvering_output = result['maneuvering_output']
                maneuvering_df = maneuvering_output['maneuvering']

                # 构建机动索引：{时间: 机动类型集合}
                self.maneuvering_index[mmsi] = {}
                for _, row in maneuvering_df.iterrows():
                    pos_time = int(row['PosTime'])
                    maneuvering_type = row['maneuvering_type']

                    if pos_time not in self.maneuvering_index[mmsi]:
                        self.maneuvering_index[mmsi][pos_time] = set()

                    if maneuvering_type != 0:
                        self.maneuvering_index[mmsi][pos_time].add(maneuvering_type)

            except Exception as e:
                print(f"⚠️  船舶 {mmsi} 机动行为识别失败: {e}")
                continue

        print(f"✅ 机动行为识别完成，处理了 {len(self.maneuvering_index)} 艘船舶")

    def step6_extract_maneuvering_moments(self):
        """Step 6: 提取机动时刻信息"""
        print("\n=== Step 6: 提取机动时刻信息 ===")

        self.overtaking_maneuvering_moments = []

        # 处理追越场景中的机动时刻
        for scene_idx, scene in enumerate(tqdm(self.filtered_overtaking_scenes, desc="机动时刻提取")):
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])
            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            # 获取场景中的所有时间点
            scene_times = np.unique(scene[:, 0])

            for time_point in scene_times:
                time_point = int(time_point)

                # 检查ship1是否有机动
                if (ship1_mmsi in self.maneuvering_index and
                        time_point in self.maneuvering_index[ship1_mmsi] and
                        len(self.maneuvering_index[ship1_mmsi][time_point]) > 0):
                    moment = self._create_moment_data(ship1_mmsi, ship2_mmsi, time_point, scene_idx)
                    if moment:
                        self.overtaking_maneuvering_moments.append(moment)

                # 检查ship2是否有机动
                if (ship2_mmsi in self.maneuvering_index and
                        time_point in self.maneuvering_index[ship2_mmsi] and
                        len(self.maneuvering_index[ship2_mmsi][time_point]) > 0):
                    moment = self._create_moment_data(ship2_mmsi, ship1_mmsi, time_point, scene_idx)
                    if moment:
                        self.overtaking_maneuvering_moments.append(moment)

        print(f"✅ 机动时刻提取完成:")
        print(f"   追越机动时刻: {len(self.overtaking_maneuvering_moments)} 个")

    def step7_analyze_position_changes(self):
        """Step 7: 分析位置变化"""
        print("\n=== Step 7: 分析位置变化 ===")

        self.overtaking_success_scenes = []
        self.position_change_details = []

        for scene_idx, scene in enumerate(tqdm(self.filtered_overtaking_scenes, desc="分析位置变化")):
            ship1_idx = int(scene[0, 2])
            ship2_idx = int(scene[0, 3])
            ship1_mmsi = self.mmsi_cache[ship1_idx]
            ship2_mmsi = self.mmsi_cache[ship2_idx]

            # 获取场景时间范围
            scene_times = np.unique(scene[:, 0])
            scene_start_time = int(scene_times[0])
            scene_end_time = int(scene_times[-1])

            # 分析位置变化
            position_changes = self._analyze_ship_position_changes(ship1_mmsi, ship2_mmsi, scene_start_time,
                                                                   scene_end_time)

            if position_changes:
                for change in position_changes:
                    success_scene = {
                        'scene_index': scene_idx,
                        'ship1_mmsi': ship1_mmsi,
                        'ship2_mmsi': ship2_mmsi,
                        'change_time': change['change_time'],
                        'overtaking_ship': change['overtaking_ship'],
                        'overtaken_ship': change['overtaken_ship'],
                        'position_before': change['position_before'],
                        'position_after': change['position_after'],
                        'time_before': change['time_before'],
                        'time_after': change['time_after'],
                        'ship1_before': change['ship1_before'],
                        'ship1_after': change['ship1_after'],
                        'ship2_before': change['ship2_before'],
                        'ship2_after': change['ship2_after'],
                        'full_scene': scene  # 保存完整场景信息
                    }
                    self.overtaking_success_scenes.append(success_scene)
                    self.position_change_details.append(change)

        print(f"✅ 位置变化分析完成:")
        print(f"   分析场景数: {len(self.filtered_overtaking_scenes)}")
        print(f"   发现追越成功场景: {len(self.overtaking_success_scenes)}")

    def step9_save_results(self):
        """Step 9: 保存结果"""
        print("\n=== Step 9: 保存结果 ===")

        # 创建结果目录
        result_dir = Path(f"result/plot_data/{self.data_time}")

        try:
            # 确保目录存在，如果不存在则创建（包括所有父目录）
            result_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 结果目录已创建/确认: {result_dir}")
        except Exception as e:
            print(f"❌ 创建目录失败: {e}")
            raise

        # 保存追越成功场景（包含完整场景信息）
        success_file = result_dir / "overtaking_success_scenes.pkl"
        try:
            with open(success_file, 'wb') as f:
                pickle.dump(self.valid_overtaking_success_scenes, f)
            print(f"✅ 追越成功场景已保存: {success_file}")
        except Exception as e:
            print(f"❌ 保存追越成功场景失败: {e}")
            raise

        # 保存完整的追越场景（用于后续分析）
        full_scenes_file = result_dir / "overtaking_full_scenes.pkl"
        try:
            with open(full_scenes_file, 'wb') as f:
                pickle.dump(self.filtered_overtaking_scenes, f)
            print(f"✅ 完整追越场景已保存: {full_scenes_file}")
        except Exception as e:
            print(f"❌ 保存完整追越场景失败: {e}")
            raise

        print(f"✅ 结果保存完成:")
        print(f"   追越成功场景文件: {success_file}")
        print(f"   完整追越场景文件: {full_scenes_file}")
        print(f"   追越成功场景: {len(self.valid_overtaking_success_scenes)} 个")
        print(f"   完整追越场景: {len(self.filtered_overtaking_scenes)} 个")

        return self.valid_overtaking_success_scenes

    def _analyze_ship_position_changes(self, ship1_mmsi, ship2_mmsi, scene_start, scene_end):
        """
        分析两艘船的位置变化
        
        Args:
            ship1_mmsi: 第一艘船MMSI
            ship2_mmsi: 第二艘船MMSI
            scene_start: 场景开始时间
            scene_end: 场景结束时间
            
        Returns:
            list: 位置变化详情列表
        """
        # 获取两船在场景时间范围内的轨迹数据
        ship1_data = self.tras_df[(self.tras_df['MMSI'] == ship1_mmsi) &
                                  (self.tras_df['PosTime'] >= scene_start) &
                                  (self.tras_df['PosTime'] <= scene_end)].sort_values('PosTime')

        ship2_data = self.tras_df[(self.tras_df['MMSI'] == ship2_mmsi) &
                                  (self.tras_df['PosTime'] >= scene_start) &
                                  (self.tras_df['PosTime'] <= scene_end)].sort_values('PosTime')

        if ship1_data.empty or ship2_data.empty:
            return []

        # 找到两船都有数据的时间点
        common_times = set(ship1_data['PosTime']) & set(ship2_data['PosTime'])
        if len(common_times) < 2:
            return []

        common_times = sorted(list(common_times))

        # 分析位置变化
        position_changes = []

        for i in range(len(common_times) - 1):
            time_before = common_times[i]
            time_after = common_times[i + 1]

            # 获取两船在前后时间点的信息
            ship1_before = ship1_data[ship1_data['PosTime'] == time_before].iloc[0]
            ship1_after = ship1_data[ship1_data['PosTime'] == time_after].iloc[0]
            ship2_before = ship2_data[ship2_data['PosTime'] == time_before].iloc[0]
            ship2_after = ship2_data[ship2_data['PosTime'] == time_after].iloc[0]

            # 判断位置关系变化
            position_before = self._get_relative_position(ship1_before, ship2_before)
            position_after = self._get_relative_position(ship1_after, ship2_after)

            # 检查是否发生追越
            if position_before != position_after:
                # 确定哪艘船是追越船
                if position_before == 'ship1_behind' and position_after == 'ship1_ahead':
                    overtaking_ship = ship1_mmsi
                    overtaken_ship = ship2_mmsi
                elif position_before == 'ship2_behind' and position_after == 'ship2_ahead':
                    overtaking_ship = ship2_mmsi
                    overtaken_ship = ship1_mmsi
                else:
                    # 其他位置变化（如并排变为前后），不认为是追越成功
                    continue

                # 创建位置变化详情
                change_detail = {
                    'change_time': time_after,
                    'overtaking_ship': overtaking_ship,
                    'overtaken_ship': overtaken_ship,
                    'position_before': position_before,
                    'position_after': position_after,
                    'time_before': time_before,
                    'time_after': time_after,
                    'ship1_before': self._create_ship_info_dict(ship1_before),
                    'ship1_after': self._create_ship_info_dict(ship1_after),
                    'ship2_before': self._create_ship_info_dict(ship2_before),
                    'ship2_after': self._create_ship_info_dict(ship2_after)
                }

                position_changes.append(change_detail)

        return position_changes

    def _get_relative_position(self, ship1_data, ship2_data):
        """
        获取两艘船的相对位置关系
        
        Args:
            ship1_data: 第一艘船数据行
            ship2_data: 第二艘船数据行
            
        Returns:
            str: 位置关系描述
        """
        # 获取船舶位置和航向
        ship1_lon, ship1_lat = ship1_data['Lon'], ship1_data['Lat']
        ship2_lon, ship2_lat = ship2_data['Lon'], ship2_data['Lat']
        ship2_cog = ship2_data['Cog']  # 以ship2的航向为参考

        # 计算从ship2到ship1的方向向量
        dx = ship1_lon - ship2_lon
        dy = ship1_lat - ship2_lat

        # 将航向角度转换为弧度
        ship2_cog_rad = np.radians(90 - ship2_cog)

        # 计算航向方向向量
        cog_dx = np.cos(ship2_cog_rad)
        cog_dy = np.sin(ship2_cog_rad)

        # 计算两个向量的点积
        dot_product = dx * cog_dx + dy * cog_dy

        # 判断相对位置
        if dot_product > 0:
            return 'ship1_ahead'  # ship1在ship2前方
        else:
            return 'ship1_behind'  # ship1在ship2后方

    def _create_ship_info_dict(self, ship_data):
        """创建船舶信息字典"""
        return {
            'mmsi': int(ship_data['MMSI']),
            'lon': float(ship_data['Lon']),
            'lat': float(ship_data['Lat']),
            'cog': float(ship_data['Cog']),
            'sog': float(ship_data['Sog']),
            'length': float(ship_data['Length']) if pd.notna(ship_data['Length']) else 100.0,
            'width': float(ship_data['Width']) if pd.notna(ship_data['Width']) else 15.0,
            'time_point': int(ship_data['PosTime'])
        }

    def _get_ship_info(self, mmsi, time_point):
        """获取指定时间点船舶的完整信息"""
        ship_data = self.tras_df[(self.tras_df['MMSI'] == mmsi) &
                                 (self.tras_df['PosTime'] == time_point)]
        if ship_data.empty:
            return None

        row = ship_data.iloc[0]
        return {
            'mmsi': int(mmsi),
            'lon': float(row['Lon']),
            'lat': float(row['Lat']),
            'cog': float(row['Cog']),
            'sog': float(row['Sog']),
            'length': float(row['Length']) if pd.notna(row['Length']) else 100.0,
            'width': float(row['Width']) if pd.notna(row['Width']) else 15.0,
            'time_point': time_point
        }

    def _create_moment_data(self, maneuvering_mmsi, other_mmsi, time_point, scene_idx):
        """创建机动时刻的数据"""
        # 获取机动船信息
        maneuvering_ship = self._get_ship_info(maneuvering_mmsi, time_point)
        # 获取非机动船信息
        other_ship = self._get_ship_info(other_mmsi, time_point)

        if maneuvering_ship is None or other_ship is None:
            return None

        return [maneuvering_ship, other_ship]

    def _calculate_distance(self, lon1, lat1, lon2, lat2):
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt

        # 转换为弧度
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])

        # 计算差值
        dlon = lon2 - lon1
        dlat = lat2 - lat1

        # 使用Haversine公式
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * asin(sqrt(a))

        # 地球半径（米）
        r = 6371000

        return c * r

    def _build_optimization_indices(self):
        """构建优化索引"""
        print("正在构建优化索引...")

        # 1. MMSI缓存：轨迹索引 -> MMSI
        self.mmsi_cache = {}
        for idx, trajectory in enumerate(self.trajectory_list):
            self.mmsi_cache[idx] = trajectory['MMSI'].iloc[0]

        # 2. 穿越船时间段索引：MMSI -> [(start, end), ...]
        self.crossing_time_index = defaultdict(list)
        for mmsi, start_time, end_time in self.crossing_ship_time_segments:
            self.crossing_time_index[mmsi].append((start_time, end_time))

        # 3. 位置查询缓存
        self.position_cache = {}

    def _get_ship_position_cached(self, mmsi, time_point):
        """带缓存的位置查询"""
        cache_key = (mmsi, time_point)
        if cache_key in self.position_cache:
            return self.position_cache[cache_key]

        result = self._get_ship_position(mmsi, time_point)
        self.position_cache[cache_key] = result
        return result

    def _both_ships_in_main_channel_cached(self, ship1_mmsi, ship2_mmsi, time_point):
        """带缓存的主航道检查"""
        ship1_pos = self._get_ship_position_cached(ship1_mmsi, time_point)
        ship2_pos = self._get_ship_position_cached(ship2_mmsi, time_point)

        if ship1_pos is None or ship2_pos is None:
            return False

        return (self._point_in_polygon(ship1_pos, self.geo_information['channel_boundary']) and
                self._point_in_polygon(ship2_pos, self.geo_information['channel_boundary']))

    def _has_crossing_ship_optimized(self, ship1_mmsi, ship2_mmsi, scene_start, scene_end):
        """优化的穿越船检查"""
        for mmsi in [ship1_mmsi, ship2_mmsi]:
            if mmsi in self.crossing_time_index:
                for crossing_start, crossing_end in self.crossing_time_index[mmsi]:
                    if scene_start < crossing_end and scene_end > crossing_start:
                        return True
        return False

    def _get_ship_position(self, mmsi, time_point):
        """获取指定时间点船舶的位置"""
        ship_data = self.tras_df[(self.tras_df['MMSI'] == mmsi) &
                                 (self.tras_df['PosTime'] == time_point)]
        if ship_data.empty:
            return None

        row = ship_data.iloc[0]
        return (float(row['Lon']), float(row['Lat']))

    def _get_ship_subregion(self, position, subregions):
        """判断船舶位置属于哪个子区域"""
        from shapely.geometry import Point

        lon, lat = position
        point = Point(lon, lat)

        for subregion_name, subregion_polygon in subregions.items():
            if point.within(subregion_polygon):
                return subregion_name

        return None

    def _get_ship_trajectory(self, idx):
        """获取船舶的完整轨迹数据"""
        ship_data = self.trajectory_list[idx]
        if ship_data.empty:
            return None

        # 按时间排序
        ship_data = ship_data.sort_values('PosTime')
        return ship_data

    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内"""
        from shapely.geometry import Point
        return Point(point[0], point[1]).within(polygon)

    def run_full_pipeline(self):
        """运行完整的追越成功场景提取流程"""
        print("🚢 开始追越成功场景提取流程...")
        print("=" * 60)

        # 执行完整流程
        self.step1_load_encounter_scenes()
        self.step2_identify_crossing_ships()
        self.step3_classify_encounter_scenes()
        self.step4_filter_overtaking_by_subregion()
        self.step5_identify_maneuvering_behaviors()
        self.step6_extract_maneuvering_moments()
        self.step7_analyze_position_changes()
        # 跳过step8筛选，直接使用step7的结果
        self.valid_overtaking_success_scenes = self.overtaking_success_scenes.copy()
        success_scenes = self.step9_save_results()

        print("\n" + "=" * 60)
        print("🎉 追越成功场景提取完成！")
        print("=" * 60)

        # 显示详细统计
        self._print_extraction_summary()

        return success_scenes

    def _print_extraction_summary(self):
        """打印提取结果摘要"""
        print("\n📊 提取结果摘要:")
        print(f"   原始会遇场景: {len(self.encounter_scenes)} 个")
        print(f"   追越场景: {len(self.overtaking_scenes)} 个")
        print(f"   筛选后追越场景: {len(self.filtered_overtaking_scenes)} 个")
        print(f"   追越机动时刻: {len(self.overtaking_maneuvering_moments)} 个")
        print(f"   发现位置变化: {len(self.overtaking_success_scenes)} 个")
        print(f"   有效追越成功: {len(self.valid_overtaking_success_scenes)} 个")

        if self.valid_overtaking_success_scenes:
            # 统计追越船分布
            overtaking_ships = set(scene['overtaking_ship'] for scene in self.valid_overtaking_success_scenes)
            overtaken_ships = set(scene['overtaken_ship'] for scene in self.valid_overtaking_success_scenes)

            print(f"\n   船舶统计:")
            print(f"   ├─ 追越船数量: {len(overtaking_ships)} 艘")
            print(f"   └─ 被追越船数量: {len(overtaken_ships)} 艘")

            # 统计时间分布
            change_times = [scene['change_time'] for scene in self.valid_overtaking_success_scenes]
            if change_times:
                time_range = f"{min(change_times)} - {max(change_times)}"
                print(f"\n   时间范围: {time_range}")


def main():
    """主函数"""
    months = [1]
    for month in tqdm(months, leave=True, desc='Month'):
        data_time = f'2024_{month}'

        # 创建追越成功场景提取器
        extractor = OvertakingSuccessExtractor(data_time)

        # 运行完整流程
        success_scenes = extractor.run_full_pipeline()

        print(f"\n📊 最终统计:")
        print(f"   输入会遇场景: {len(extractor.encounter_scenes)} 个")
        print(f"   输出追越成功场景: {len(success_scenes)} 个")
        print(f"   数据格式: 包含完整场景信息的追越成功场景")


if __name__ == '__main__':
    main()
